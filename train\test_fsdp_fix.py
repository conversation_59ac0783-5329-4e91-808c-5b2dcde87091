#!/usr/bin/env python3
# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

"""
测试FSDP修复的脚本
"""

import torch
import os
from transformers import AutoModelForCausalLM, AutoTokenizer
from rm import RewardModelWithValueHead

def test_model_creation():
    """测试模型创建是否正常"""
    print("Testing model creation...")
    
    # 使用一个小模型进行测试
    model_name = "microsoft/DialoGPT-small"  # 使用一个小模型进行测试
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            trust_remote_code=True, 
            use_fast=True,
            padding_side="right",
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            model_name, 
            trust_remote_code=True,
            torch_dtype=torch.float32,
            use_cache=False,
        )
        
        # 创建RewardModelWithValueHead
        reward_model = RewardModelWithValueHead(pretrained_model=model, linear_tpye="single")
        
        print("✓ Model creation successful")
        print(f"✓ Model type: {type(reward_model)}")
        print(f"✓ Pretrained model type: {type(reward_model.pretrained_model)}")
        print(f"✓ Value head type: {type(reward_model.v_head)}")
        
        # 测试前向传播
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        test_input = "Hello, this is a test."
        inputs = tokenizer(test_input, return_tensors="pt", padding=True, truncation=True)
        
        with torch.no_grad():
            outputs = reward_model(**inputs)
            
        print(f"✓ Forward pass successful, output shape: {outputs.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def test_fsdp_compatibility():
    """测试FSDP兼容性"""
    print("\nTesting FSDP compatibility...")
    
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        from fsdp_config import setup_fsdp_for_training, get_fsdp_wrap_policy
        
        print("✓ FSDP imports successful")
        
        # 测试包装策略
        policy = get_fsdp_wrap_policy()
        print(f"✓ FSDP wrap policy created: {policy}")
        
        return True
        
    except Exception as e:
        print(f"✗ FSDP compatibility test failed: {e}")
        return False

def test_trainer_config():
    """测试训练器配置"""
    print("\nTesting trainer configuration...")
    
    try:
        from trl import RewardConfig, ModelConfig
        from transformers import HfArgumentParser
        
        # 创建测试配置
        config = RewardConfig(
            output_dir="./test_output",
            per_device_train_batch_size=1,
            per_device_eval_batch_size=1,
            num_train_epochs=1,
            max_length=128,
            learning_rate=1e-5,
        )
        
        print("✓ RewardConfig creation successful")
        print(f"✓ Config type: {type(config)}")
        
        # 测试FSDP配置
        config.fsdp = "full_shard auto_wrap"
        print(f"✓ FSDP config set: {config.fsdp}")
        
        return True
        
    except Exception as e:
        print(f"✗ Trainer config test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("FSDP Fix Test Suite")
    print("=" * 50)
    
    tests = [
        test_model_creation,
        test_fsdp_compatibility,
        test_trainer_config,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Results Summary")
    print("=" * 50)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    total_passed = sum(results)
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 All tests passed! The FSDP fix should work.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return total_passed == total_tests

if __name__ == "__main__":
    main()
