# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.
import warnings
import torch
import os
from rm import *
from datasets import load_dataset, concatenate_datasets
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer, HfArgumentParser, set_seed
from typing import Dict
from trl import ModelConfig, RewardConfig
import random
import numpy as np
IGNORE_INDEX = 0

def rank0_print(rank, *args):
    if rank == 0:
        print(*args)

def unwrap_fsdp_if_wrapped(model):
    """如果模型被FSDP包装，则解包装"""
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        if isinstance(model, FSDP):
            print(f"Unwrapping FSDP from {type(model)}")
            return model._fsdp_wrapped_module
        return model
    except ImportError:
        return model

def check_and_fix_fsdp_wrapping(model):
    """检查并修复FSDP重复包装问题"""
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        
        # 检查子模块是否被包装
        if hasattr(model, 'pretrained_model') and isinstance(model.pretrained_model, FSDP):
            print("Warning: pretrained_model is already FSDP wrapped, unwrapping...")
            model.pretrained_model = model.pretrained_model._fsdp_wrapped_module
            
        if hasattr(model, 'v_head') and isinstance(model.v_head, FSDP):
            print("Warning: v_head is already FSDP wrapped, unwrapping...")
            model.v_head = model.v_head._fsdp_wrapped_module
            
        # 确保模型本身没有被包装
        if isinstance(model, FSDP):
            print("Warning: model itself is FSDP wrapped, unwrapping...")
            model = model._fsdp_wrapped_module
            
        return model
        
    except ImportError:
        return model
        
if __name__ == "__main__":
    parser = HfArgumentParser((RewardConfig, ModelConfig))
    parser.add_argument('--pair_json_path', type=str, default="/home/<USER>/teamdrive/teamdrive/xy/xy/mcts/0924/qwen2SFT__ORM_filter.json")
    parser.add_argument('--test_pair_json_path', type=str, default=None)
    parser.add_argument('--metrics_path', type=str, default=None)
    parser.add_argument('--linear_tpye', type=str, default="single")
    parser.add_argument('--attn_impl', type=str, default="eager")
    config, model_config, remain_args = parser.parse_args_into_dataclasses()
    
    config.gradient_checkpointing_kwargs = dict(use_reentrant=False)
    config.pair_json_path = remain_args.pair_json_path

    from accelerate import Accelerator

    accelerator = Accelerator()
    rank = accelerator.process_index
    print(rank)
    tokenizer = AutoTokenizer.from_pretrained(
        model_config.model_name_or_path, 
        trust_remote_code=True, 
        use_fast=True,
        padding_side="right",
        split_special_tokens=False,
    )
    model = AutoModelForCausalLM.from_pretrained(
        model_config.model_name_or_path, 
        trust_remote_code=True,
        torch_dtype=torch.bfloat16 if remain_args.attn_impl == "flash_attention_2" else torch.float32,
        attn_implementation=remain_args.attn_impl,
        use_cache=False,
    )
    
    # 确保基础模型没有被FSDP包装
    model = unwrap_fsdp_if_wrapped(model)
    rank0_print(rank, f"Base model type after unwrapping: {type(model)}")
    
    # 创建RewardModel
    model = RewardModelWithValueHead(pretrained_model=model, linear_tpye=remain_args.linear_tpye)
    
    # 检查并修复任何FSDP包装问题
    model = check_and_fix_fsdp_wrapping(model)
    rank0_print(rank, f"RewardModel type after fixing: {type(model)}")
    
    # 打印模型结构以确认没有FSDP包装
    if rank == 0:
        print("Model structure check:")
        print(f"  pretrained_model type: {type(model.pretrained_model)}")
        print(f"  v_head type: {type(model.v_head)}")
        print(f"  model type: {type(model)}")

    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if model.config.pad_token_id is None:
        model.config.pad_token_id = model.config.eos_token_id
    tokenizer.add_special_tokens(
        {
            "additional_special_tokens": ['<code>', '<end_of_step>', '<end_of_code>', '<o>', '<end_of_output>', '<answer>', '<end_of_answer>', '<|user|>', '<|assistant|>', '<refine>', '<end_of_refine>', '\n<|assistant|>']
        },
        replace_additional_special_tokens=False,
    )
    model.pretrained_model.resize_token_embeddings(len(tokenizer))

    ################
    # Dataset
    ################
    raw_datasets = load_dataset("json", data_files=config.pair_json_path)
    raw_datasets = raw_datasets.map(
        lambda x: preprocess_function(x, tokenizer, config.max_length),
        batched=True,
        num_proc=4,
        remove_columns=raw_datasets["train"].column_names,
        load_from_cache_file=False,
        desc="Running tokenizer on dataset",
    )
    
    if remain_args.test_pair_json_path:
        test_datasets = load_dataset("json", data_files=remain_args.test_pair_json_path)
        test_datasets = test_datasets.map(
            lambda x: preprocess_function(x, tokenizer, config.max_length),
            batched=True,
            num_proc=4,
            remove_columns=test_datasets["train"].column_names,
            load_from_cache_file=False,
            desc="Running tokenizer on dataset",
        )
        raw_datasets["test"] = test_datasets["train"]
    else:
        raw_datasets = raw_datasets["train"].train_test_split(test_size=0.1, seed=42)
    
    rank0_print(rank, 'after preprocess', raw_datasets['train'].column_names)
    train_dataset = raw_datasets["train"]
    eval_dataset = raw_datasets["test"]
    rank0_print(rank, 'After filtering, trainset size:', len(train_dataset), 'testset size:', len(eval_dataset))

    ################
    # Training
    ################
    rank0_print(rank, config)
    rank0_print(rank, train_dataset.column_names)

    trainer = RMTrainer(
        model=model,
        tokenizer=tokenizer,
        args=config,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        data_collator=PairwiseDataCollatorWithPadding(
            tokenizer=tokenizer,
            max_length=config.max_length,
            padding='max_length'
            ),
        compute_metrics=ComputeAccuracy()
    )
    
    # 在训练前再次检查模型状态
    rank0_print(rank, "Final model check before training:")
    rank0_print(rank, f"  Model type: {type(trainer.model)}")
    if hasattr(trainer.model, 'pretrained_model'):
        rank0_print(rank, f"  Pretrained model type: {type(trainer.model.pretrained_model)}")
    if hasattr(trainer.model, 'v_head'):
        rank0_print(rank, f"  V head type: {type(trainer.model.v_head)}")
    
    trainer.train()
    trainer.save_model(config.output_dir)
    trainer.save_state()
    metrics = trainer.evaluate()
    trainer.log_metrics("eval", metrics)
    rank0_print(rank, metrics)
    import json
    import os
    if remain_args.metrics_path:
        os.makedirs(os.path.dirname(remain_args.metrics_path), exist_ok=True)
        with open(remain_args.metrics_path, 'w') as f:
            metrics['test_pair_json_path'] = remain_args.test_pair_json_path
            metrics['model_name_or_path'] = model_config.model_name_or_path
            json.dump(metrics, f)
