[{"index": 0, "question": "<PERSON><PERSON> and <PERSON> play the following game. <PERSON><PERSON> thinks of a positive integer $N$ not exceeding 5000. Then she fixes 20 distinct positive integers $a_{1}, a_{2}, \\ldots, a_{20}$ such that, for each $k=1,2, \\ldots, 20$, the numbers $N$ and $a_{k}$ are congruent modulo $k$. By a move, <PERSON> tells <PERSON><PERSON> a set $S$ of positive integers not exceeding 20 , and she tells him back the set $\\left\\{a_{k}: k \\in S\\right\\}$ without spelling out which number corresponds to which index. How many moves does <PERSON> need to determine for sure the number <PERSON><PERSON> thought of?", "answer": "2"}, {"index": 1, "question": "Given a positive integer $n$, determine the largest real number $\\mu$ satisfying the following condition: for every $4 n$-point configuration $C$ in an open unit square $U$, there exists an open rectangle in $U$, whose sides are parallel to those of $U$, which contains exactly one point of $C$, and has an area greater than or equal to $\\mu$.", "answer": "\\frac{1}{2n+2}"}, {"index": 2, "question": "\nFind (in closed form) the difference between the number of positive integers at most $2^{2017}$ with even weight and the number of positive integers at most $2^{2017}$ with odd weight.", "answer": "2^{1009}"}, {"index": 3, "question": "Determine all positive integers $n$ satisfying the following condition: for every monic polynomial $P$ of degree at most $n$ with integer coefficients, there exists a positive integer $k \\leq n$, and $k+1$ distinct integers $x_{1}, x_{2}, \\ldots, x_{k+1}$ such that\n\n\n\n$$\n\nP\\left(x_{1}\\right)+P\\left(x_{2}\\right)+\\cdots+P\\left(x_{k}\\right)=P\\left(x_{k+1}\\right) .\n\n$$\n\n\nNote. A polynomial is monic if the coefficient of the highest power is one.", "answer": "2"}, {"index": 4, "question": "Let $n$ be an integer greater than 1 and let $X$ be an $n$-element set. A non-empty collection of subsets $A_{1}, \\ldots, A_{k}$ of $X$ is tight if the union $A_{1} \\cup \\cdots \\cup A_{k}$ is a proper subset of $X$ and no element of $X$ lies in exactly one of the $A_{i}$ s. Find the largest cardinality of a collection of proper non-empty subsets of $X$, no non-empty subcollection of which is tight.\n\n\n\nNote. A subset $A$ of $X$ is proper if $A \\neq X$. The sets in a collection are assumed to be distinct. The whole collection is assumed to be a subcollection.", "answer": "2n-2"}, {"index": 5, "question": "Determine all prime numbers $p$ and all positive integers $x$ and $y$ satisfying $x^{3}+y^{3}=$ $p(x y+p)$.", "answer": "(1,8,19),(2,7,13),(4,5,7)"}, {"index": 6, "question": "Let $n \\geqslant 2$ be an integer, and let $f$ be a $4 n$-variable polynomial with real coefficients. Assume that, for any $2 n$ points $\\left(x_{1}, y_{1}\\right), \\ldots,\\left(x_{2 n}, y_{2 n}\\right)$ in the plane, $f\\left(x_{1}, y_{1}, \\ldots, x_{2 n}, y_{2 n}\\right)=0$ if and only if the points form the vertices of a regular $2 n$-gon in some order, or are all equal.\n\n\n\nDetermine the smallest possible degree of $f$.", "answer": "2n"}, {"index": 7, "question": "For a positive integer $a$, define a sequence of integers $x_{1}, x_{2}, \\ldots$ by letting $x_{1}=a$ and $x_{n+1}=2 x_{n}+1$ for $n \\geq 1$. Let $y_{n}=2^{x_{n}}-1$. Determine the largest possible $k$ such that, for some positive integer $a$, the numbers $y_{1}, \\ldots, y_{k}$ are all prime.", "answer": "2"}, {"index": 8, "question": "Let $n$ be a positive integer and fix $2 n$ distinct points on a circumference. Split these points into $n$ pairs and join the points in each pair by an arrow (i.e., an oriented line segment). The resulting configuration is good if no two arrows cross, and there are no arrows $\\overrightarrow{A B}$ and $\\overrightarrow{C D}$ such that $A B C D$ is a convex quadrangle oriented clockwise. Determine the number of good configurations.", "answer": "\\binom{2n}{n}"}, {"index": 9, "question": "Given positive integers $m$ and $n \\geq m$, determine the largest number of dominoes $(1 \\times 2$ or $2 \\times 1$ rectangles) that can be placed on a rectangular board with $m$ rows and $2 n$ columns consisting of cells $(1 \\times 1$ squares $)$ so that:\n\n\n\n(i) each domino covers exactly two adjacent cells of the board;\n\n\n\n(ii) no two dominoes overlap;\n\n\n\n(iii) no two form a $2 \\times 2$ square; and\n\n\n\n(iv) the bottom row of the board is completely covered by $n$ dominoes.", "answer": "mn-\\lfloorm/2\\rfloor"}, {"index": 10, "question": "A cubic sequence is a sequence of integers given by $a_{n}=n^{3}+b n^{2}+c n+d$, where $b, c$ and $d$ are integer constants and $n$ ranges over all integers, including negative integers.\n\nDetermine the possible values of $a_{2015} \\cdot a_{2016}$ for a cubic sequence satisfying the condition in part (a).", "answer": "0"}, {"index": 11, "question": "Find all functions $f: \\mathbb{R}^{+} \\rightarrow \\mathbb{R}^{+}$ such that\n\n$$\nf(x+f(y))=f(x+y)+f(y)\\tag{1}\n$$\n\nfor all $x, y \\in \\mathbb{R}^{+}$. (Symbol $\\mathbb{R}^{+}$denotes the set of all positive real numbers.)", "answer": "f(x)=2x"}, {"index": 12, "question": "Let $n>1$ be an integer. In the space, consider the set\n$$\nS=\\{(x, y, z) \\mid x, y, z \\in\\{0,1, \\ldots, n\\}, x+y+z>0\\}\n$$\nFind the smallest number of planes that jointly contain all $(n+1)^{3}-1$ points of $S$ but none of them passes through the origin.", "answer": "3n"}, {"index": 13, "question": "Find all positive integers $n$, for which the numbers in the set $S=\\{1,2, \\ldots, n\\}$ can be colored red and blue, with the following condition being satisfied: the set $S \\times S \\times S$ contains exactly 2007 ordered triples $(x, y, z)$ such that (i) $x, y, z$ are of the same color and (ii) $x+y+z$ is divisible by $n$.", "answer": "69,84"}, {"index": 14, "question": "Determine the smallest positive real number $k$ with the following property.\n\nLet $A B C D$ be a convex quadrilateral, and let points $A_{1}, B_{1}, C_{1}$ and $D_{1}$ lie on sides $A B, B C$, $C D$ and $D A$, respectively. Consider the areas of triangles $A A_{1} D_{1}, B B_{1} A_{1}, C C_{1} B_{1}$, and $D D_{1} C_{1}$; let $S$ be the sum of the two smallest ones, and let $S_{1}$ be the area of quadrilateral $A_{1} B_{1} C_{1} D_{1}$. Then we always have $k S_{1} \\geq S$.", "answer": "k=1"}, {"index": 15, "question": "Find all pairs $(k, n)$ of positive integers for which $7^{k}-3^{n}$ divides $k^{4}+n^{2}$.", "answer": "(2,4)"}, {"index": 16, "question": "Find all surjective functions $f: \\mathbb{N} \\rightarrow \\mathbb{N}$ such that for every $m, n \\in \\mathbb{N}$ and every prime $p$, the number $f(m+n)$ is divisible by $p$ if and only if $f(m)+f(n)$ is divisible by $p$.\n\n( $\\mathbb{N}$ is the set of all positive integers.)", "answer": "f(n)=n"}, {"index": 17, "question": "Determine all pairs $(f, g)$ of functions from the set of positive integers to itself that satisfy\n\n$$\nf^{g(n)+1}(n)+g^{f(n)}(n)=f(n+1)-g(n+1)+1\n$$\n\nfor every positive integer $n$. Here, $f^{k}(n)$ means $\\underbrace{f(f(\\ldots f}_{k}(n) \\ldots))$.", "answer": "f(n)=n,g(n)=1"}, {"index": 18, "question": "Determine the greatest positive integer $k$ that satisfies the following property: The set of positive integers can be partitioned into $k$ subsets $A_{1}, A_{2}, \\ldots, A_{k}$ such that for all integers $n \\geq 15$ and all $i \\in\\{1,2, \\ldots, k\\}$ there exist two distinct elements of $A_{i}$ whose sum is $n$.", "answer": "3"}, {"index": 19, "question": "Let $m$ be a positive integer and consider a checkerboard consisting of $m$ by $m$ unit squares. At the midpoints of some of these unit squares there is an ant. At time 0, each ant starts moving with speed 1 parallel to some edge of the checkerboard. When two ants moving in opposite directions meet, they both turn $90^{\\circ}$ clockwise and continue moving with speed 1 . When more than two ants meet, or when two ants moving in perpendicular directions meet, the ants continue moving in the same direction as before they met. When an ant reaches one of the edges of the checkerboard, it falls off and will not re-appear.\n\nConsidering all possible starting positions, determine the latest possible moment at which the last ant falls off the checkerboard or prove that such a moment does not necessarily exist.", "answer": "\\frac{3m}{2}-1"}, {"index": 20, "question": "On a square table of 2011 by 2011 cells we place a finite number of napkins that each cover a square of 52 by 52 cells. In each cell we write the number of napkins covering it, and we record the maximal number $k$ of cells that all contain the same nonzero number. Considering all possible napkin configurations, what is the largest value of $k$ ?", "answer": "3986729"}, {"index": 21, "question": "For each positive integer $k$, let $t(k)$ be the largest odd divisor of $k$. Determine all positive integers $a$ for which there exists a positive integer $n$ such that all the differences\n\n$$\nt(n+a)-t(n), \\quad t(n+a+1)-t(n+1), \\quad \\ldots, \\quad t(n+2 a-1)-t(n+a-1)\n$$\n\nare divisible by 4 .", "answer": "1,3,5"}, {"index": 22, "question": "Let $x_{1}, \\ldots, x_{100}$ be nonnegative real numbers such that $x_{i}+x_{i+1}+x_{i+2} \\leq 1$ for all $i=1, \\ldots, 100$ (we put $x_{101}=x_{1}, x_{102}=x_{2}$ ). Find the maximal possible value of the sum\n\n$$\nS=\\sum_{i=1}^{100} x_{i} x_{i+2}\n$$", "answer": "\\frac{25}{2}"}, {"index": 23, "question": "Denote by $\\mathbb{Q}^{+}$the set of all positive rational numbers. Determine all functions $f: \\mathbb{Q}^{+} \\rightarrow \\mathbb{Q}^{+}$ which satisfy the following equation for all $x, y \\in \\mathbb{Q}^{+}$:\n\n$$\nf\\left(f(x)^{2} y\\right)=x^{3} f(x y)\n\\tag{1}\n$$", "answer": "f(x)=\\frac{1}{x}"}, {"index": 24, "question": "On some planet, there are $2^{N}$ countries $(N \\geq 4)$. Each country has a flag $N$ units wide and one unit high composed of $N$ fields of size $1 \\times 1$, each field being either yellow or blue. No two countries have the same flag.\n\nWe say that a set of $N$ flags is diverse if these flags can be arranged into an $N \\times N$ square so that all $N$ fields on its main diagonal will have the same color. Determine the smallest positive integer $M$ such that among any $M$ distinct flags, there exist $N$ flags forming a diverse set.", "answer": "M=2^{N-2}+1"}, {"index": 25, "question": "2500 chess kings have to be placed on a $100 \\times 100$ chessboard so that\n\n(i) no king can capture any other one (i.e. no two kings are placed in two squares sharing a common vertex);\n\n(ii) each row and each column contains exactly 25 kings.\n\nFind the number of such arrangements. (Two arrangements differing by rotation or symmetry are supposed to be different.)", "answer": "2"}, {"index": 26, "question": "Find the least positive integer $n$ for which there exists a set $\\left\\{s_{1}, s_{2}, \\ldots, s_{n}\\right\\}$ consisting of $n$ distinct positive integers such that\n\n$$\n\\left(1-\\frac{1}{s_{1}}\\right)\\left(1-\\frac{1}{s_{2}}\\right) \\ldots\\left(1-\\frac{1}{s_{n}}\\right)=\\frac{51}{2010}\n$$", "answer": "39"}, {"index": 27, "question": "Find all pairs $(m, n)$ of nonnegative integers for which\n\n$$\nm^{2}+2 \\cdot 3^{n}=m\\left(2^{n+1}-1\\right)\n\\tag{1}\n$$", "answer": "(6,3),(9,3),(9,5),(54,5)"}, {"index": 28, "question": "Find the smallest number $n$ such that there exist polynomials $f_{1}, f_{2}, \\ldots, f_{n}$ with rational coefficients satisfying\n\n$$\nx^{2}+7=f_{1}(x)^{2}+f_{2}(x)^{2}+\\cdots+f_{n}(x)^{2} .\n$$", "answer": "5"}, {"index": 29, "question": "Determine the smallest number $M$ such that the inequality\n\n$$\n\\left|a b\\left(a^{2}-b^{2}\\right)+b c\\left(b^{2}-c^{2}\\right)+c a\\left(c^{2}-a^{2}\\right)\\right| \\leq M\\left(a^{2}+b^{2}+c^{2}\\right)^{2}\n$$\n\nholds for all real numbers $a, b, c$.", "answer": "M=\\frac{9}{32}\\sqrt{2}"}, {"index": 30, "question": "A diagonal of a regular 2006-gon is called odd if its endpoints divide the boundary into two parts, each composed of an odd number of sides. Sides are also regarded as odd diagonals.\n\nSuppose the 2006-gon has been dissected into triangles by 2003 nonintersecting diagonals. Find the maximum possible number of isosceles triangles with two odd sides.", "answer": "1003"}, {"index": 31, "question": "In triangle $A B C$, let $J$ be the centre of the excircle tangent to side $B C$ at $A_{1}$ and to the extensions of sides $A C$ and $A B$ at $B_{1}$ and $C_{1}$, respectively. Suppose that the lines $A_{1} B_{1}$ and $A B$ are perpendicular and intersect at $D$. Let $E$ be the foot of the perpendicular from $C_{1}$ to line $D J$. Determine the angles $\\angle B E A_{1}$ and $\\angle A E B_{1}$.", "answer": "\\angleBEA_{1}=90,\\angleAEB_{1}=90"}, {"index": 32, "question": "Determine all pairs $(x, y)$ of integers satisfying the equation\n\n$$\n1+2^{x}+2^{2 x+1}=y^{2}\n$$", "answer": "(0,2),(0,-2),(4,23),(4,-23)"}, {"index": 33, "question": "Given a positive integer $n$, find the smallest value of $\\left\\lfloor\\frac{a_{1}}{1}\\right\\rfloor+\\left\\lfloor\\frac{a_{2}}{2}\\right\\rfloor+\\cdots+\\left\\lfloor\\frac{a_{n}}{n}\\right\\rfloor$ over all permutations $\\left(a_{1}, a_{2}, \\ldots, a_{n}\\right)$ of $(1,2, \\ldots, n)$.", "answer": "\\left\\lfloor\\log_{2}n\\right\\rfloor+1"}, {"index": 34, "question": "Let $n \\geqslant 3$ be an integer. An integer $m \\geqslant n+1$ is called $n$-colourful if, given infinitely many marbles in each of $n$ colours $C_{1}, C_{2}, \\ldots, C_{n}$, it is possible to place $m$ of them around a circle so that in any group of $n+1$ consecutive marbles there is at least one marble of colour $C_{i}$ for each $i=1, \\ldots, n$.\n\nProve that there are only finitely many positive integers which are not $n$-colourful. Find the largest among them.", "answer": "m_{\\max}=n^{2}-n-1"}, {"index": 35, "question": "Determine the largest $N$ for which there exists a table $T$ of integers with $N$ rows and 100 columns that has the following properties:\n\n(i) Every row contains the numbers 1,2, ., 100 in some order.\n\n(ii) For any two distinct rows $r$ and $s$, there is a column $c$ such that $|T(r, c)-T(s, c)| \\geqslant 2$.\n\nHere $T(r, c)$ means the number at the intersection of the row $r$ and the column $c$.", "answer": "\\frac{100!}{2^{50}}"}, {"index": 36, "question": "Determine all integers $n \\geqslant 1$ for which there exists a pair of positive integers $(a, b)$ such that no cube of a prime divides $a^{2}+b+3$ and\n\n$$\n\\frac{a b+3 b+8}{a^{2}+b+3}=n\n$$", "answer": "2"}, {"index": 37, "question": "Find all positive integers $n$ with the following property: the $k$ positive divisors of $n$ have a permutation $\\left(d_{1}, d_{2}, \\ldots, d_{k}\\right)$ such that for every $i=1,2, \\ldots, k$, the number $d_{1}+\\cdots+d_{i}$ is a perfect square.", "answer": "1,3"}, {"index": 38, "question": "Let $q$ be a real number. <PERSON><PERSON><PERSON> has a napkin with ten distinct real numbers written on it, and he writes the following three lines of real numbers on the blackboard:\n\n- In the first line, <PERSON><PERSON><PERSON> writes down every number of the form $a-b$, where $a$ and $b$ are two (not necessarily distinct) numbers on his napkin.\n- In the second line, <PERSON><PERSON><PERSON> writes down every number of the form $q a b$, where $a$ and $b$ are two (not necessarily distinct) numbers from the first line.\n- In the third line, <PERSON><PERSON><PERSON> writes down every number of the form $a^{2}+b^{2}-c^{2}-d^{2}$, where $a, b, c, d$ are four (not necessarily distinct) numbers from the first line.\n\nDetermine all values of $q$ such that, regardless of the numbers on <PERSON><PERSON><PERSON>'s napkin, every number in the second line is also a number in the third line.", "answer": "-2,0,2"}, {"index": 39, "question": "An integer $n \\geqslant 3$ is given. We call an $n$-tuple of real numbers $\\left(x_{1}, x_{2}, \\ldots, x_{n}\\right)$ Shiny if for each permutation $y_{1}, y_{2}, \\ldots, y_{n}$ of these numbers we have\n\n$$\n\\sum_{i=1}^{n-1} y_{i} y_{i+1}=y_{1} y_{2}+y_{2} y_{3}+y_{3} y_{4}+\\cdots+y_{n-1} y_{n} \\geqslant-1\n$$\n\nFind the largest constant $K=K(n)$ such that\n\n$$\n\\sum_{1 \\leqslant i<j \\leqslant n} x_{i} x_{j} \\geqslant K\n$$\n\nholds for every Shiny $n$-tuple $\\left(x_{1}, x_{2}, \\ldots, x_{n}\\right)$.", "answer": "-(n-1)/2"}, {"index": 40, "question": "Let $n>1$ be an integer. An $n \\times n \\times n$ cube is composed of $n^{3}$ unit cubes. Each unit cube is painted with one color. For each $n \\times n \\times 1$ box consisting of $n^{2}$ unit cubes (of any of the three possible orientations), we consider the set of the colors present in that box (each color is listed only once). This way, we get $3 n$ sets of colors, split into three groups according to the orientation. It happens that for every set in any group, the same set appears in both of the other groups. Determine, in terms of $n$, the maximal possible number of colors that are present.", "answer": "\\frac{n(n+1)(2n+1)}{6}"}, {"index": 41, "question": "Let $n$ be a given positive integer. In the Cartesian plane, each lattice point with nonnegative coordinates initially contains a butterfly, and there are no other butterflies. The neighborhood of a lattice point $c$ consists of all lattice points within the axis-aligned $(2 n+1) \\times$ $(2 n+1)$ square centered at $c$, apart from $c$ itself. We call a butterfly lonely, crowded, or comfortable, depending on whether the number of butterflies in its neighborhood $N$ is respectively less than, greater than, or equal to half of the number of lattice points in $N$.\n\nEvery minute, all lonely butterflies fly away simultaneously. This process goes on for as long as there are any lonely butterflies. Assuming that the process eventually stops, determine the number of comfortable butterflies at the final state.", "answer": "n^{2}+1"}, {"index": 42, "question": "There are 2017 mutually external circles drawn on a blackboard, such that no two are tangent and no three share a common tangent. A tangent segment is a line segment that is a common tangent to two circles, starting at one tangent point and ending at the other one. <PERSON> is drawing tangent segments on the blackboard, one at a time, so that no tangent segment intersects any other circles or previously drawn tangent segments. <PERSON> keeps drawing tangent segments until no more can be drawn. Find all possible numbers of tangent segments when he stops drawing.", "answer": "6048"}, {"index": 43, "question": "Call a rational number short if it has finitely many digits in its decimal expansion. For a positive integer $m$, we say that a positive integer $t$ is $m$-tastic if there exists a number $c \\in\\{1,2,3, \\ldots, 2017\\}$ such that $\\frac{10^{t}-1}{c \\cdot m}$ is short, and such that $\\frac{10^{k}-1}{c \\cdot m}$ is not short for any $1 \\leqslant k<t$. Let $S(m)$ be the set of $m$-tastic numbers. Consider $S(m)$ for $m=1,2, \\ldots$ What is the maximum number of elements in $S(m)$ ?", "answer": "807"}, {"index": 44, "question": "Find all pairs $(p, q)$ of prime numbers with $p>q$ for which the number\n\n$$\n\\frac{(p+q)^{p+q}(p-q)^{p-q}-1}{(p+q)^{p-q}(p-q)^{p+q}-1}\n$$\n\nis an integer.", "answer": "(3,2)"}, {"index": 45, "question": "Find the smallest positive integer $n$, or show that no such $n$ exists, with the following property: there are infinitely many distinct $n$-tuples of positive rational numbers $\\left(a_{1}, a_{2}, \\ldots, a_{n}\\right)$ such that both\n\n$$\na_{1}+a_{2}+\\cdots+a_{n} \\quad \\text { and } \\quad \\frac{1}{a_{1}}+\\frac{1}{a_{2}}+\\cdots+\\frac{1}{a_{n}}\n$$\n\nare integers.", "answer": "3"}, {"index": 46, "question": "Find the smallest real constant $C$ such that for any positive real numbers $a_{1}, a_{2}, a_{3}, a_{4}$ and $a_{5}$ (not necessarily distinct), one can always choose distinct subscripts $i, j, k$ and $l$ such that\n\n$$\n\\left|\\frac{a_{i}}{a_{j}}-\\frac{a_{k}}{a_{l}}\\right| \\leqslant C .\\tag{1}\n$$", "answer": "\\frac{1}{2}"}, {"index": 47, "question": "The equation\n\n$$\n(x-1)(x-2) \\cdots(x-2016)=(x-1)(x-2) \\cdots(x-2016)\n$$\n\nis written on the board. One tries to erase some linear factors from both sides so that each side still has at least one factor, and the resulting equation has no real roots. Find the least number of linear factors one needs to erase to achieve this.", "answer": "2016"}, {"index": 48, "question": "Determine the largest real number $a$ such that for all $n \\geqslant 1$ and for all real numbers $x_{0}, x_{1}, \\ldots, x_{n}$ satisfying $0=x_{0}<x_{1}<x_{2}<\\cdots<x_{n}$, we have\n\n$$\n\\frac{1}{x_{1}-x_{0}}+\\frac{1}{x_{2}-x_{1}}+\\cdots+\\frac{1}{x_{n}-x_{n-1}} \\geqslant a\\left(\\frac{2}{x_{1}}+\\frac{3}{x_{2}}+\\cdots+\\frac{n+1}{x_{n}}\\right) .\\tag{1}\n$$", "answer": "\\frac{4}{9}"}, {"index": 49, "question": "Find all positive integers $n$ for which all positive divisors of $n$ can be put into the cells of a rectangular table under the following constraints:\n\n- each cell contains a distinct divisor;\n- the sums of all rows are equal; and\n- the sums of all columns are equal.", "answer": "1"}, {"index": 50, "question": "Let $n$ be a positive integer. Determine the smallest positive integer $k$ with the following property: it is possible to mark $k$ cells on a $2 n \\times 2 n$ board so that there exists a unique partition of the board into $1 \\times 2$ and $2 \\times 1$ dominoes, none of which contains two marked cells.", "answer": "2n"}, {"index": 51, "question": "Define $P(n)=n^{2}+n+1$. For any positive integers $a$ and $b$, the set\n\n$$\n\\{P(a), P(a+1), P(a+2), \\ldots, P(a+b)\\}\n$$\n\nis said to be fragrant if none of its elements is relatively prime to the product of the other elements. Determine the smallest size of a fragrant set.", "answer": "6"}, {"index": 52, "question": "Denote by $\\mathbb{N}$ the set of all positive integers. Find all functions $f: \\mathbb{N} \\rightarrow \\mathbb{N}$ such that for all positive integers $m$ and $n$, the integer $f(m)+f(n)-m n$ is nonzero and divides $m f(m)+n f(n)$.", "answer": "f(n)=n^{2}"}, {"index": 53, "question": "Let $n$ be a positive integer, and set $N=2^{n}$. Determine the smallest real number $a_{n}$ such that, for all real $x$,\n\n$$\n\\sqrt[N]{\\frac{x^{2 N}+1}{2}} \\leqslant a_{n}(x-1)^{2}+x\n$$", "answer": "\\frac{N}{2}"}, {"index": 54, "question": "Let $\\mathcal{A}$ denote the set of all polynomials in three variables $x, y, z$ with integer coefficients. Let $\\mathcal{B}$ denote the subset of $\\mathcal{A}$ formed by all polynomials which can be expressed as\n\n$$\n(x+y+z) P(x, y, z)+(x y+y z+z x) Q(x, y, z)+x y z R(x, y, z)\n$$\n\nwith $P, Q, R \\in \\mathcal{A}$. Find the smallest non-negative integer $n$ such that $x^{i} y^{j} z^{k} \\in \\mathcal{B}$ for all nonnegative integers $i, j, k$ satisfying $i+j+k \\geqslant n$.", "answer": "4"}, {"index": 55, "question": "Suppose that $a, b, c, d$ are positive real numbers satisfying $(a+c)(b+d)=a c+b d$. Find the smallest possible value of\n\n$$\nS=\\frac{a}{b}+\\frac{b}{c}+\\frac{c}{d}+\\frac{d}{a}\n$$", "answer": "8"}, {"index": 56, "question": "Let $\\mathbb{R}^{+}$be the set of positive real numbers. Determine all functions $f: \\mathbb{R}^{+} \\rightarrow \\mathbb{R}^{+}$ such that, for all positive real numbers $x$ and $y$,\n\n$$\nf(x+f(x y))+y=f(x) f(y)+1\n\\tag{*}\n$$", "answer": "f(x)=x+1"}, {"index": 57, "question": "Let $n$ be an integer with $n \\geqslant 2$. On a slope of a mountain, $n^{2}$ checkpoints are marked, numbered from 1 to $n^{2}$ from the bottom to the top. Each of two cable car companies, $A$ and $B$, operates $k$ cable cars numbered from 1 to $k$; each cable car provides a transfer from some checkpoint to a higher one. For each company, and for any $i$ and $j$ with $1 \\leqslant i<j \\leqslant k$, the starting point of car $j$ is higher than the starting point of car $i$; similarly, the finishing point of car $j$ is higher than the finishing point of car $i$. Say that two checkpoints are linked by some company if one can start from the lower checkpoint and reach the higher one by using one or more cars of that company (no movement on foot is allowed).\n\nDetermine the smallest $k$ for which one can guarantee that there are two checkpoints that are linked by each of the two companies.", "answer": "n^{2}-n+1"}, {"index": 58, "question": "The Fibonacci numbers $F_{0}, F_{1}, F_{2}, \\ldots$ are defined inductively by $F_{0}=0, F_{1}=1$, and $F_{n+1}=F_{n}+F_{n-1}$ for $n \\geqslant 1$. Given an integer $n \\geqslant 2$, determine the smallest size of a set $S$ of integers such that for every $k=2,3, \\ldots, n$ there exist some $x, y \\in S$ such that $x-y=F_{k}$.", "answer": "\\lceiln/2\\rceil+1"}, {"index": 59, "question": "Players $A$ and $B$ play a game on a blackboard that initially contains 2020 copies of the number 1. In every round, player $A$ erases two numbers $x$ and $y$ from the blackboard, and then player $B$ writes one of the numbers $x+y$ and $|x-y|$ on the blackboard. The game terminates as soon as, at the end of some round, one of the following holds:\n\n(1) one of the numbers on the blackboard is larger than the sum of all other numbers;\n\n(2) there are only zeros on the blackboard.\n\nPlayer $B$ must then give as many cookies to player $A$ as there are numbers on the blackboard. Player $A$ wants to get as many cookies as possible, whereas player $B$ wants to give as few as possible. Determine the number of cookies that $A$ receives if both players play optimally.", "answer": "7"}, {"index": 60, "question": "Let $n$ be a positive integer. <PERSON> has $n$ coins lined up on his desk, each showing heads or tails. He repeatedly does the following operation: if there are $k$ coins showing heads and $k>0$, then he flips the $k^{\\text {th }}$ coin over; otherwise he stops the process. (For example, the process starting with THT would be THT $\\rightarrow H H T \\rightarrow H T T \\rightarrow T T T$, which takes three steps.)\n\nLetting $C$ denote the initial configuration (a sequence of $n H$ 's and $T$ 's), write $\\ell(C)$ for the number of steps needed before all coins show $T$. Show that this number $\\ell(C)$ is finite, and determine its average value over all $2^{n}$ possible initial configurations $C$.", "answer": "\\frac{1}{4}n(n+1)"}, {"index": 61, "question": "On a flat plane in Camelot, <PERSON> builds a labyrinth $\\mathfrak{L}$ consisting of $n$ walls, each of which is an infinite straight line. No two walls are parallel, and no three walls have a common point. <PERSON> then paints one side of each wall entirely red and the other side entirely blue.\n\nAt the intersection of two walls there are four corners: two diagonally opposite corners where a red side and a blue side meet, one corner where two red sides meet, and one corner where two blue sides meet. At each such intersection, there is a two-way door connecting the two diagonally opposite corners at which sides of different colours meet.\n\nAfter <PERSON> paints the walls, <PERSON><PERSON> then places some knights in the labyrinth. The knights can walk through doors, but cannot walk through walls.\n\nLet $k(\\mathfrak{L})$ be the largest number $k$ such that, no matter how <PERSON> paints the labyrinth $\\mathfrak{L}$, <PERSON><PERSON> can always place at least $k$ knights such that no two of them can ever meet. For each $n$, what are all possible values for $k(\\mathfrak{L})$, where $\\mathfrak{L}$ is a labyrinth with $n$ walls?", "answer": "k=n+1"}, {"index": 62, "question": "There are 60 empty boxes $B_{1}, \\ldots, B_{60}$ in a row on a table and an unlimited supply of pebbles. Given a positive integer $n$, <PERSON> and <PERSON> play the following game.\n\nIn the first round, <PERSON> takes $n$ pebbles and distributes them into the 60 boxes as she wishes. Each subsequent round consists of two steps:\n\n(a) <PERSON> chooses an integer $k$ with $1 \\leqslant k \\leqslant 59$ and splits the boxes into the two groups $B_{1}, \\ldots, B_{k}$ and $B_{k+1}, \\ldots, B_{60}$.\n\n(b) <PERSON> picks one of these two groups, adds one pebble to each box in that group, and removes one pebble from each box in the other group.\n\n<PERSON> wins if, at the end of any round, some box contains no pebbles. Find the smallest $n$ such that <PERSON> can prevent <PERSON> from winning.", "answer": "960"}, {"index": 63, "question": "For any two different real numbers $x$ and $y$, we define $D(x, y)$ to be the unique integer $d$ satisfying $2^{d} \\leqslant|x-y|<2^{d+1}$. Given a set of reals $\\mathcal{F}$, and an element $x \\in \\mathcal{F}$, we say that the scales of $x$ in $\\mathcal{F}$ are the values of $D(x, y)$ for $y \\in \\mathcal{F}$ with $x \\neq y$.\n\nLet $k$ be a given positive integer. Suppose that each member $x$ of $\\mathcal{F}$ has at most $k$ different scales in $\\mathcal{F}$ (note that these scales may depend on $x$ ). What is the maximum possible size of $\\mathcal{F}$ ?", "answer": "2^{k}"}, {"index": 64, "question": "Find all pairs $(m, n)$ of positive integers satisfying the equation\n\n$$\n\\left(2^{n}-1\\right)\\left(2^{n}-2\\right)\\left(2^{n}-4\\right) \\cdots\\left(2^{n}-2^{n-1}\\right)=m !\n\\tag{1}\n$$", "answer": "(1,1),(3,2)"}, {"index": 65, "question": "Find all triples $(a, b, c)$ of positive integers such that $a^{3}+b^{3}+c^{3}=(a b c)^{2}$.", "answer": "(1,2,3),(1,3,2),(2,1,3),(2,3,1),(3,1,2),(3,2,1)"}, {"index": 66, "question": "Determine all functions $f: \\mathbb{Z} \\rightarrow \\mathbb{Z}$ with the property that\n\n$$\nf(x-f(y))=f(f(x))-f(y)-1\n\\tag{1}\n$$\n\nholds for all $x, y \\in \\mathbb{Z}$.", "answer": "f(x)=-1,f(x)=x+1"}, {"index": 67, "question": "Let $n$ be a fixed positive integer. Find the maximum possible value of\n\n$$\n\\sum_{1 \\leqslant r<s \\leqslant 2 n}(s-r-n) x_{r} x_{s}\n$$\n\nwhere $-1 \\leqslant x_{i} \\leqslant 1$ for all $i=1,2, \\ldots, 2 n$.", "answer": "n(n-1)"}, {"index": 68, "question": "Find all functions $f: \\mathbb{R} \\rightarrow \\mathbb{R}$ satisfying the equation\n\n$$\nf(x+f(x+y))+f(x y)=x+f(x+y)+y f(x)\\tag{1}\n$$\n\nfor all real numbers $x$ and $y$.", "answer": "f(x)=x,f(x)=2-x"}, {"index": 69, "question": "For a finite set $A$ of positive integers, we call a partition of $A$ into two disjoint nonempty subsets $A_{1}$ and $A_{2}$ good if the least common multiple of the elements in $A_{1}$ is equal to the greatest common divisor of the elements in $A_{2}$. Determine the minimum value of $n$ such that there exists a set of $n$ positive integers with exactly 2015 good partitions.", "answer": "3024"}, {"index": 70, "question": "Let $A B C$ be an acute triangle, and let $M$ be the midpoint of $A C$. A circle $\\omega$ passing through $B$ and $M$ meets the sides $A B$ and $B C$ again at $P$ and $Q$, respectively. Let $T$ be the point such that the quadrilateral $B P T Q$ is a parallelogram. Suppose that $T$ lies on the circumcircle of the triangle $A B C$. Determine all possible values of $B T / B M$.", "answer": "\\sqrt{2}"}, {"index": 71, "question": "Determine all triples $(a, b, c)$ of positive integers for which $a b-c, b c-a$, and $c a-b$ are powers of 2 .\n\nExplanation: A power of 2 is an integer of the form $2^{n}$, where $n$ denotes some nonnegative integer.", "answer": "(2,2,2),(2,2,3),(2,3,2),(3,2,2),(2,6,11),(2,11,6),(6,2,11),(6,11,2),(11,2,6),(11,6,2),(3,5,7),(3,7,5),(5,3,7),(5,7,3),(7,3,5),(7,5,3)"}, {"index": 72, "question": "Let $\\mathbb{Z}_{>0}$ denote the set of positive integers. For any positive integer $k$, a function $f: \\mathbb{Z}_{>0} \\rightarrow \\mathbb{Z}_{>0}$ is called $k$-good if $\\operatorname{gcd}(f(m)+n, f(n)+m) \\leqslant k$ for all $m \\neq n$. Find all $k$ such that there exists a $k$-good function.", "answer": "k\\geqslant2"}, {"index": 73, "question": "For every positive integer $n$ with prime factorization $n=\\prod_{i=1}^{k} p_{i}^{\\alpha_{i}}$, define\n\n$$\n\\mho(n)=\\sum_{i: p_{i}>10^{100}} \\alpha_{i}\\tag{1}\n$$\n\nThat is, $\\mho(n)$ is the number of prime factors of $n$ greater than $10^{100}$, counted with multiplicity.\n\nFind all strictly increasing functions $f: \\mathbb{Z} \\rightarrow \\mathbb{Z}$ such that\n\n$$\n\\mho(f(a)-f(b)) \\leqslant \\mho(a-b) \\quad \\text { for all integers } a \\text { and } b \\text { with } a>b \\text {. }\n$$", "answer": "f(x)=ax+b,wherebisanarbitraryinteger,andaisanarbitrarypositiveintegerwith\\mho(a)=0"}, {"index": 74, "question": "For a sequence $x_{1}, x_{2}, \\ldots, x_{n}$ of real numbers, we define its price as\n\n$$\n\\max _{1 \\leqslant i \\leqslant n}\\left|x_{1}+\\cdots+x_{i}\\right|\n$$\n\nGiven $n$ real numbers, <PERSON> and <PERSON> want to arrange them into a sequence with a low price. Di<PERSON> Dave checks all possible ways and finds the minimum possible price $D$. <PERSON><PERSON><PERSON>, on the other hand, chooses $x_{1}$ such that $\\left|x_{1}\\right|$ is as small as possible; among the remaining numbers, he chooses $x_{2}$ such that $\\left|x_{1}+x_{2}\\right|$ is as small as possible, and so on. Thus, in the $i^{\\text {th }}$ step he chooses $x_{i}$ among the remaining numbers so as to minimise the value of $\\left|x_{1}+x_{2}+\\cdots+x_{i}\\right|$. In each step, if several numbers provide the same value, <PERSON> chooses one at random. Finally he gets a sequence with price $G$.\n\nFind the least possible constant $c$ such that for every positive integer $n$, for every collection of $n$ real numbers, and for every possible sequence that <PERSON> might obtain, the resulting values satisfy the inequality $G \\leqslant c D$.", "answer": "2"}, {"index": 75, "question": "Determine all functions $f: \\mathbb{Z} \\rightarrow \\mathbb{Z}$ satisfying\n\n$$\nf(f(m)+n)+f(m)=f(n)+f(3 m)+2014\n\\tag{1}\n$$\n\nfor all integers $m$ and $n$.", "answer": "f(n)=2n+1007"}, {"index": 76, "question": "Consider all polynomials $P(x)$ with real coefficients that have the following property: for any two real numbers $x$ and $y$ one has\n\n$$\n\\left|y^{2}-P(x)\\right| \\leqslant 2|x| \\text { if and only if }\\left|x^{2}-P(y)\\right| \\leqslant 2|y|\n\\tag{1}\n$$\n\nDetermine all possible values of $P(0)$.", "answer": "(-\\infty,0)\\cup\\{1\\}."}, {"index": 77, "question": "Let $n \\geqslant 2$ be an integer. Consider an $n \\times n$ chessboard divided into $n^{2}$ unit squares. We call a configuration of $n$ rooks on this board happy if every row and every column contains exactly one rook. Find the greatest positive integer $k$ such that for every happy configuration of rooks, we can find a $k \\times k$ square without a rook on any of its $k^{2}$ unit squares.", "answer": "\\lfloor\\sqrt{n-1}\\rfloor"}, {"index": 78, "question": "We are given an infinite deck of cards, each with a real number on it. For every real number $x$, there is exactly one card in the deck that has $x$ written on it. Now two players draw disjoint sets $A$ and $B$ of 100 cards each from this deck. We would like to define a rule that declares one of them a winner. This rule should satisfy the following conditions:\n\n1. The winner only depends on the relative order of the 200 cards: if the cards are laid down in increasing order face down and we are told which card belongs to which player, but not what numbers are written on them, we can still decide the winner.\n2. If we write the elements of both sets in increasing order as $A=\\left\\{a_{1}, a_{2}, \\ldots, a_{100}\\right\\}$ and $B=\\left\\{b_{1}, b_{2}, \\ldots, b_{100}\\right\\}$, and $a_{i}>b_{i}$ for all $i$, then $A$ beats $B$.\n3. If three players draw three disjoint sets $A, B, C$ from the deck, $A$ beats $B$ and $B$ beats $C$, then $A$ also beats $C$.\n\nHow many ways are there to define such a rule? Here, we consider two rules as different if there exist two sets $A$ and $B$ such that $A$ beats $B$ according to one rule, but $B$ beats $A$ according to the other.", "answer": "100"}, {"index": 79, "question": "Let $n \\geqslant 2$ be an integer, and let $A_{n}$ be the set\n\n$$\nA_{n}=\\left\\{2^{n}-2^{k} \\mid k \\in \\mathbb{Z}, 0 \\leqslant k<n\\right\\} .\n$$\n\nDetermine the largest positive integer that cannot be written as the sum of one or more (not necessarily distinct) elements of $A_{n}$.", "answer": "(n-2)2^{n}+1"}, {"index": 80, "question": "Let $k \\geqslant 2$ be an integer. Find the smallest integer $n \\geqslant k+1$ with the property that there exists a set of $n$ distinct real numbers such that each of its elements can be written as a sum of $k$ other distinct elements of the set.", "answer": "n=k+4"}, {"index": 81, "question": "Let $\\mathbb{R}_{>0}$ be the set of positive real numbers. Find all functions $f: \\mathbb{R}_{>0} \\rightarrow \\mathbb{R}_{>0}$ such that, for every $x \\in \\mathbb{R}_{>0}$, there exists a unique $y \\in \\mathbb{R}_{>0}$ satisfying\n\n$$\nx f(y)+y f(x) \\leqslant 2 .\n$$", "answer": "f(x)=\\frac{1}{x}"}, {"index": 82, "question": "Find all positive integers $n \\geqslant 2$ for which there exist $n$ real numbers $a_{1}<\\cdots<a_{n}$ and a real number $r>0$ such that the $\\frac{1}{2} n(n-1)$ differences $a_{j}-a_{i}$ for $1 \\leqslant i<j \\leqslant n$ are equal, in some order, to the numbers $r^{1}, r^{2}, \\ldots, r^{\\frac{1}{2} n(n-1)}$.", "answer": "2,3,4"}, {"index": 83, "question": "$A \\pm 1 \\text{-}sequence$ is a sequence of 2022 numbers $a_{1}, \\ldots, a_{2022}$, each equal to either +1 or -1 . Determine the largest $C$ so that, for any $\\pm 1 -sequence$, there exists an integer $k$ and indices $1 \\leqslant t_{1}<\\ldots<t_{k} \\leqslant 2022$ so that $t_{i+1}-t_{i} \\leqslant 2$ for all $i$, and\n\n$$\n\\left|\\sum_{i=1}^{k} a_{t_{i}}\\right| \\geqslant C\n$$", "answer": "506"}, {"index": 84, "question": "In each square of a garden shaped like a $2022 \\times 2022$ board, there is initially a tree of height 0 . A gardener and a lumberjack alternate turns playing the following game, with the gardener taking the first turn:\n\n- The gardener chooses a square in the garden. Each tree on that square and all the surrounding squares (of which there are at most eight) then becomes one unit taller.\n- The lumberjack then chooses four different squares on the board. Each tree of positive height on those squares then becomes one unit shorter.\n\nWe say that a tree is majestic if its height is at least $10^{6}$. Determine the largest number $K$ such that the gardener can ensure there are eventually $K$ majestic trees on the board, no matter how the lumberjack plays.", "answer": "2271380"}, {"index": 85, "question": "Lucy starts by writing $s$ integer-valued 2022-tuples on a blackboard. After doing that, she can take any two (not necessarily distinct) tuples $\\mathbf{v}=\\left(v_{1}, \\ldots, v_{2022}\\right)$ and $\\mathbf{w}=\\left(w_{1}, \\ldots, w_{2022}\\right)$ that she has already written, and apply one of the following operations to obtain a new tuple:\n\n$$\n\\begin{aligned}\n& \\mathbf{v}+\\mathbf{w}=\\left(v_{1}+w_{1}, \\ldots, v_{2022}+w_{2022}\\right) \\\\\n& \\mathbf{v} \\vee \\mathbf{w}=\\left(\\max \\left(v_{1}, w_{1}\\right), \\ldots, \\max \\left(v_{2022}, w_{2022}\\right)\\right)\n\\end{aligned}\n$$\n\nand then write this tuple on the blackboard.\n\nIt turns out that, in this way, Lucy can write any integer-valued 2022-tuple on the blackboard after finitely many steps. What is the smallest possible number $s$ of tuples that she initially wrote?", "answer": "3"}, {"index": 86, "question": "<PERSON> fills the fields of an $n \\times n$ board with numbers from 1 to $n^{2}$, each number being used exactly once. She then counts the total number of good paths on the board. A good path is a sequence of fields of arbitrary length (including 1) such that:\n\n(i) The first field in the sequence is one that is only adjacent to fields with larger numbers,\n\n(ii) Each subsequent field in the sequence is adjacent to the previous field,\n\n(iii) The numbers written on the fields in the sequence are in increasing order.\n\nTwo fields are considered adjacent if they share a common side. Find the smallest possible number of good paths <PERSON> can obtain, as a function of $n$.", "answer": "2n^{2}-2n+1"}, {"index": 87, "question": "Let $\\mathbb{Z}_{\\geqslant 0}$ be the set of non-negative integers, and let $f: \\mathbb{Z}_{\\geqslant 0} \\times \\mathbb{Z}_{\\geqslant 0} \\rightarrow \\mathbb{Z}_{\\geqslant 0}$ be a bijection such that whenever $f\\left(x_{1}, y_{1}\\right)>f\\left(x_{2}, y_{2}\\right)$, we have $f\\left(x_{1}+1, y_{1}\\right)>f\\left(x_{2}+1, y_{2}\\right)$ and $f\\left(x_{1}, y_{1}+1\\right)>f\\left(x_{2}, y_{2}+1\\right)$.\n\nLet $N$ be the number of pairs of integers $(x, y)$, with $0 \\leqslant x, y<100$, such that $f(x, y)$ is odd. Find the smallest and largest possible value of $N$.", "answer": "2500,7500"}, {"index": 88, "question": "A number is called Norwegian if it has three distinct positive divisors whose sum is equal to 2022. Determine the smallest Norwegian number.\n\n(Note: The total number of positive divisors of a Norwegian number is allowed to be larger than 3.)", "answer": "1344"}, {"index": 89, "question": "Find all positive integers $n>2$ such that\n\n$$\nn ! \\mid \\prod_{\\substack{p<q \\leqslant n \\\\ p, q \\text { primes }}}(p+q)\n$$", "answer": "7"}, {"index": 90, "question": "Find all triples of positive integers $(a, b, p)$ with $p$ prime and\n\n$$\na^{p}=b !+p\n$$", "answer": "(2,2,2),(3,4,3)"}, {"index": 91, "question": "Let $\\mathbb{Q}_{>0}$ denote the set of all positive rational numbers. Determine all functions $f: \\mathbb{Q}_{>0} \\rightarrow \\mathbb{Q}_{>0}$ satisfying\n\n$$\nf\\left(x^{2} f(y)^{2}\\right)=f(x)^{2} f(y)\n\\tag{*}\n$$\n\nfor all $x, y \\in \\mathbb{Q}_{>0}$.", "answer": "f(x)=1"}, {"index": 92, "question": "Let $a_{0}, a_{1}, a_{2}, \\ldots$ be a sequence of real numbers such that $a_{0}=0, a_{1}=1$, and for every $n \\geqslant 2$ there exists $1 \\leqslant k \\leqslant n$ satisfying\n\n$$\na_{n}=\\frac{a_{n-1}+\\cdots+a_{n-k}}{k}\n$$\n\nFind the maximal possible value of $a_{2018}-a_{2017}$.", "answer": "\\frac{2016}{2017^{2}}"}, {"index": 93, "question": "Find the maximal value of\n\n$$\nS=\\sqrt[3]{\\frac{a}{b+7}}+\\sqrt[3]{\\frac{b}{c+7}}+\\sqrt[3]{\\frac{c}{d+7}}+\\sqrt[3]{\\frac{d}{a+7}}\n$$\n\nwhere $a, b, c, d$ are nonnegative real numbers which satisfy $a+b+c+d=100$.", "answer": "\\frac{8}{\\sqrt[3]{7}}"}, {"index": 94, "question": "<PERSON><PERSON> and <PERSON><PERSON> play a game on a $20 \\times 20$ chessboard. In the beginning the board is empty. In every turn, <PERSON><PERSON> places a black knight on an empty square in such a way that his new knight does not attack any previous knights. Then <PERSON><PERSON> places a white queen on an empty square. The game gets finished when somebody cannot move.\n\nFind the maximal positive $K$ such that, regardless of the strategy of <PERSON><PERSON>, <PERSON><PERSON> can put at least $K$ knights on the board.", "answer": "100"}, {"index": 95, "question": "Let $k$ be a positive integer. The organising committee of a tennis tournament is to schedule the matches for $2 k$ players so that every two players play once, each day exactly one match is played, and each player arrives to the tournament site the day of his first match, and departs the day of his last match. For every day a player is present on the tournament, the committee has to pay 1 coin to the hotel. The organisers want to design the schedule so as to minimise the total cost of all players' stays. Determine this minimum cost.", "answer": "k\\left(4k^{2}+k-1\\right)/2"}, {"index": 96, "question": "A circle $\\omega$ of radius 1 is given. A collection $T$ of triangles is called good, if the following conditions hold:\n\n(i) each triangle from $T$ is inscribed in $\\omega$;\n\n(ii) no two triangles from $T$ have a common interior point.\n\nDetermine all positive real numbers $t$ such that, for each positive integer $n$, there exists a good collection of $n$ triangles, each of perimeter greater than $t$.", "answer": "t(0,4]"}, {"index": 97, "question": "Let $n$ be a positive integer. Find the smallest integer $k$ with the following property: Given any real numbers $a_{1}, \\ldots, a_{d}$ such that $a_{1}+a_{2}+\\cdots+a_{d}=n$ and $0 \\leqslant a_{i} \\leqslant 1$ for $i=1,2, \\ldots, d$, it is possible to partition these numbers into $k$ groups (some of which may be empty) such that the sum of the numbers in each group is at most 1 .", "answer": "k=2n-1"}, {"index": 98, "question": "In the plane, 2013 red points and 2014 blue points are marked so that no three of the marked points are collinear. One needs to draw $k$ lines not passing through the marked points and dividing the plane into several regions. The goal is to do it in such a way that no region contains points of both colors.\n\nFind the minimal value of $k$ such that the goal is attainable for every possible configuration of 4027 points.", "answer": "2013"}, {"index": 99, "question": "Let $\\mathbb{Z}_{>0}$ be the set of positive integers. Find all functions $f: \\mathbb{Z}_{>0} \\rightarrow \\mathbb{Z}_{>0}$ such that\n\n$$\nm^{2}+f(n) \\mid m f(m)+n\n$$\n\nfor all positive integers $m$ and $n$.", "answer": "f(n)=n"}, {"index": 100, "question": "Find the largest possible integer $k$, such that the following statement is true:\n\nLet 2009 arbitrary non-degenerated triangles be given. In every triangle the three sides are colored, such that one is blue, one is red and one is white. Now, for every color separately, let us sort the lengths of the sides. We obtain\n\n$$\n\\begin{aligned}\nb_{1} \\leq b_{2} \\leq \\ldots \\leq b_{2009} & \\text { the lengths of the blue sides } \\\\\nr_{1} \\leq r_{2} \\leq \\ldots \\leq r_{2009} & \\text { the lengths of the red sides, } \\\\\n\\text { and } \\quad & w_{1} \\leq w_{2} \\leq \\ldots \\leq w_{2009} \\quad \\text { the lengths of the white sides. }\n\\end{aligned}\n$$\n\nThen there exist $k$ indices $j$ such that we can form a non-degenerated triangle with side lengths $b_{j}, r_{j}, w_{j}$.", "answer": "1"}, {"index": 101, "question": "Determine all functions $f$ from the set of positive integers into the set of positive integers such that for all $x$ and $y$ there exists a non degenerated triangle with sides of lengths\n\n$$\nx, \\quad f(y) \\text { and } f(y+f(x)-1) .\n$$", "answer": "f(z)=z"}, {"index": 102, "question": "For any integer $n \\geq 2$, let $N(n)$ be the maximal number of triples $\\left(a_{i}, b_{i}, c_{i}\\right), i=1, \\ldots, N(n)$, consisting of nonnegative integers $a_{i}, b_{i}$ and $c_{i}$ such that the following two conditions are satisfied:\n\n(1) $a_{i}+b_{i}+c_{i}=n$ for all $i=1, \\ldots, N(n)$,\n\n(2) If $i \\neq j$, then $a_{i} \\neq a_{j}, b_{i} \\neq b_{j}$ and $c_{i} \\neq c_{j}$.\n\nDetermine $N(n)$ for all $n \\geq 2$.", "answer": "N(n)=\\left\\lfloor\\frac{2n}{3}\\right\\rfloor+1"}, {"index": 103, "question": "On a $999 \\times 999$ board a limp rook can move in the following way: From any square it can move to any of its adjacent squares, i.e. a square having a common side with it, and every move must be a turn, i.e. the directions of any two consecutive moves must be perpendicular. A nonintersecting route of the limp rook consists of a sequence of pairwise different squares that the limp rook can visit in that order by an admissible sequence of moves. Such a non-intersecting route is called cyclic, if the limp rook can, after reaching the last square of the route, move directly to the first square of the route and start over.\n\nHow many squares does the longest possible cyclic, non-intersecting route of a limp rook visit?", "answer": "996000"}, {"index": 104, "question": "Let $A B C$ be a triangle with $A B=A C$. The angle bisectors of $A$ and $B$ meet the sides $B C$ and $A C$ in $D$ and $E$, respectively. Let $K$ be the incenter of triangle $A D C$. Suppose that $\\angle B E K=45^{\\circ}$. Find all possible values of $\\angle B A C$.", "answer": "90^{\\circ},60^{\\circ}"}, {"index": 105, "question": "Find all positive integers $n$ such that there exists a sequence of positive integers $a_{1}, a_{2}, \\ldots, a_{n}$ satisfying\n\n$$\na_{k+1}=\\frac{a_{k}^{2}+1}{a_{k-1}+1}-1\n$$\n\nfor every $k$ with $2 \\leq k \\leq n-1$.", "answer": "1,2,3,4"}, {"index": 106, "question": "In the plane we consider rectangles whose sides are parallel to the coordinate axes and have positive length. Such a rectangle will be called a box. Two boxes intersect if they have a common point in their interior or on their boundary.\n\nFind the largest $n$ for which there exist $n$ boxes $B_{1}, \\ldots, B_{n}$ such that $B_{i}$ and $B_{j}$ intersect if and only if $i \\not \\equiv j \\pm 1(\\bmod n)$.", "answer": "6"}, {"index": 107, "question": "In the coordinate plane consider the set $S$ of all points with integer coordinates. For a positive integer $k$, two distinct points $A, B \\in S$ will be called $k$-friends if there is a point $C \\in S$ such that the area of the triangle $A B C$ is equal to $k$. A set $T \\subset S$ will be called a $k$-clique if every two points in $T$ are $k$-friends. Find the least positive integer $k$ for which there exists a $k$-clique with more than 200 elements.", "answer": "180180"}, {"index": 108, "question": "Let $n$ and $k$ be fixed positive integers of the same parity, $k \\geq n$. We are given $2 n$ lamps numbered 1 through $2 n$; each of them can be on or off. At the beginning all lamps are off. We consider sequences of $k$ steps. At each step one of the lamps is switched (from off to on or from on to off).\n\nLet $N$ be the number of $k$-step sequences ending in the state: lamps $1, \\ldots, n$ on, lamps $n+1, \\ldots, 2 n$ off.\n\nLet $M$ be the number of $k$-step sequences leading to the same state and not touching lamps $n+1, \\ldots, 2 n$ at all.\n\nFind the ratio $N / M$.", "answer": "2^{k-n}"}, {"index": 109, "question": "Find all functions $f: \\mathbb{R} \\rightarrow \\mathbb{R}$ that satisfy the conditions\n\n$$\nf(1+x y)-f(x+y)=f(x) f(y) \\text { for all } x, y \\in \\mathbb{R}\n$$\n\nand $f(-1) \\neq 0$.", "answer": "f(x)=x-1"}, {"index": 110, "question": "Let $n \\geq 1$ be an integer. What is the maximum number of disjoint pairs of elements of the set $\\{1,2, \\ldots, n\\}$ such that the sums of the different pairs are different integers not exceeding $n$ ?", "answer": "\\lfloor\\frac{2n-1}{5}\\rfloor"}, {"index": 111, "question": "In a $999 \\times 999$ square table some cells are white and the remaining ones are red. Let $T$ be the number of triples $\\left(C_{1}, C_{2}, C_{3}\\right)$ of cells, the first two in the same row and the last two in the same column, with $C_{1}$ and $C_{3}$ white and $C_{2}$ red. Find the maximum value $T$ can attain.", "answer": "\\frac{4\\cdot999^{4}}{27}"}, {"index": 112, "question": "Players $A$ and $B$ play a game with $N \\geq 2012$ coins and 2012 boxes arranged around a circle. Initially $A$ distributes the coins among the boxes so that there is at least 1 coin in each box. Then the two of them make moves in the order $B, A, B, A, \\ldots$ by the following rules:\n\n- On every move of his $B$ passes 1 coin from every box to an adjacent box.\n- On every move of hers $A$ chooses several coins that were not involved in $B$ 's previous move and are in different boxes. She passes every chosen coin to an adjacent box.\n\nPlayer $A$ 's goal is to ensure at least 1 coin in each box after every move of hers, regardless of how $B$ plays and how many moves are made. Find the least $N$ that enables her to succeed.", "answer": "4022"}, {"index": 113, "question": "Find all triples $(x, y, z)$ of positive integers such that $x \\leq y \\leq z$ and\n\n$$\nx^{3}\\left(y^{3}+z^{3}\\right)=2012(x y z+2) \\text {. }\n$$", "answer": "(2,251,252)"}, {"index": 114, "question": "Find all functions $f: \\mathbb{Q} \\rightarrow \\mathbb{Q}$ such that the equation\n\nholds for all rational numbers $x$ and $y$.\n\n$$\nf(x f(x)+y)=f(y)+x^{2}\n$$\n\nHere, $\\mathbb{Q}$ denotes the set of rational numbers.", "answer": "f(x)=x,f(x)=-x"}, {"index": 115, "question": "A plane has a special point $O$ called the origin. Let $P$ be a set of 2021 points in the plane, such that\n\n(i) no three points in $P$ lie on a line and\n\n(ii) no two points in $P$ lie on a line through the origin.\n\nA triangle with vertices in $P$ is $f a t$, if $O$ is strictly inside the triangle. Find the maximum number of fat triangles.", "answer": "2021\\cdot505\\cdot337"}, {"index": 116, "question": "Find the smallest positive integer $k$ for which there exist a colouring of the positive integers $\\mathbb{Z}_{>0}$ with $k$ colours and a function $f: \\mathbb{Z}_{>0} \\rightarrow \\mathbb{Z}_{>0}$ with the following two properties:\n\n(i) For all positive integers $m, n$ of the same colour, $f(m+n)=f(m)+f(n)$.\n\n(ii) There are positive integers $m, n$ such that $f(m+n) \\neq f(m)+f(n)$.\n\nIn a colouring of $\\mathbb{Z}_{>0}$ with $k$ colours, every integer is coloured in exactly one of the $k$ colours. In both (i) and (ii) the positive integers $m, n$ are not necessarily different.", "answer": "k=3"}, {"index": 117, "question": "Let $m$ be a positive integer. Consider a $4 m \\times 4 m$ array of square unit cells. Two different cells are related to each other if they are in either the same row or in the same column. No cell is related to itself. Some cells are coloured blue, such that every cell is related to at least two blue cells. Determine the minimum number of blue cells.", "answer": "6m"}, {"index": 118, "question": "Let $m>1$ be an integer. A sequence $a_{1}, a_{2}, a_{3}, \\ldots$ is defined by $a_{1}=a_{2}=1$, $a_{3}=4$, and for all $n \\geq 4$,\n\n$$\na_{n}=m\\left(a_{n-1}+a_{n-2}\\right)-a_{n-3} .\n$$\n\nDetermine all integers $m$ such that every term of the sequence is a square.", "answer": "1,2"}, {"index": 119, "question": "The $n$ contestants of an EGMO are named $C_{1}, \\ldots, C_{n}$. After the competition they queue in front of the restaurant according to the following rules.\n\n- The Jury chooses the initial order of the contestants in the queue.\n- Every minute, the Jury chooses an integer $i$ with $1 \\leq i \\leq n$.\n    - If contestant $C_{i}$ has at least $i$ other contestants in front of her, she pays one euro to the Jury and moves forward in the queue by exactly $i$ positions.\n    - If contestant $C_{i}$ has fewer than $i$ other contestants in front of her, the restaurant opens and the process ends.\nDetermine for every $n$ the maximum number of euros that the Jury can collect by cunningly choosing the initial order and the sequence of moves.", "answer": "2^{n}-n-1"}, {"index": 120, "question": "Find all triples $(a, b, c)$ of real numbers such that $a b+b c+$ $c a=1$ and\n\n$$\na^{2} b+c=b^{2} c+a=c^{2} a+b \\text {. }\n$$", "answer": "(0,1,1),(0,-1,-1),(1,0,1),(-1,0,-1),(1,1,0),(-1,-1,0),\\left(\\frac{1}{\\sqrt{3}},\\frac{1}{\\sqrt{3}},\\frac{1}{\\sqrt{3}}\\right),\\left(-\\frac{1}{\\sqrt{3}},-\\frac{1}{\\sqrt{3}},-\\frac{1}{\\sqrt{3}}\\right)"}, {"index": 121, "question": "Let $n$ be a positive integer. Dominoes are placed on a $2 n \\times 2 n$ board in such a way that every cell of the board is adjacent to exactly one cell covered by a domino. For each $n$, determine the largest number of dominoes that can be placed in this way.\n\n(A domino is a tile of size $2 \\times 1$ or $1 \\times 2$. Dominoes are placed on the board in such a way that each domino covers exactly two cells of the board, and dominoes do not overlap. Two cells are said to be adjacent if they are different and share a common side.)", "answer": "\\frac{n(n+1)}{2}"}, {"index": 122, "question": "Given a positive integer $n \\geq 2$, determine the largest positive integer $N$ for which there exist $N+1$ real numbers $a_{0}, a_{1}, \\ldots, a_{N}$ such that\n\n(1) $a_{0}+a_{1}=-\\frac{1}{n}$, and\n\n(2) $\\left(a_{k}+a_{k-1}\\right)\\left(a_{k}+a_{k+1}\\right)=a_{k-1}-a_{k+1}$ for $1 \\leq k \\leq N-1$.", "answer": "N=n"}, {"index": 123, "question": "Determine all integers $m$ for which the $m \\times m$ square can be dissected into five rectangles, the side lengths of which are the integers $1,2,3, \\ldots, 10$ in some order.", "answer": "11,13"}, {"index": 124, "question": "Let $k$ be a positive integer. <PERSON> has a dictionary $\\mathcal{D}$ consisting of some $k$-letter strings containing only the letters $A$ and $B$. <PERSON> would like to write either the letter $A$ or the letter $B$ in each cell of a $k \\times k$ grid so that each column contains a string from $\\mathcal{D}$ when read from top-to-bottom and each row contains a string from $\\mathcal{D}$ when read from left-to-right.\n\nWhat is the smallest integer $m$ such that if $\\mathcal{D}$ contains at least $m$ different strings, then <PERSON> can fill her grid in this manner, no matter what strings are in $\\mathcal{D}$ ?", "answer": "2^{k-1}"}, {"index": 125, "question": "In an increasing sequence of numbers with an odd number of terms, the difference between any two consecutive terms is a constant $d$, and the middle term is 302 . When the last 4 terms are removed from the sequence, the middle term of the resulting sequence is 296. What is the value of $d$ ?", "answer": "3"}, {"index": 126, "question": "There are two increasing sequences of five consecutive integers, each of which have the property that the sum of the squares of the first three integers in the sequence equals the sum of the squares of the last two. Determine these two sequences.", "answer": "10,11,12,13,14,-2,-1,0,1,2"}, {"index": 127, "question": "If $f(t)=\\sin \\left(\\pi t-\\frac{\\pi}{2}\\right)$, what is the smallest positive value of $t$ at which $f(t)$ attains its minimum value?", "answer": "2"}, {"index": 128, "question": "Determine all integer values of $x$ such that $\\left(x^{2}-3\\right)\\left(x^{2}+5\\right)<0$.", "answer": "-1,0,1"}, {"index": 129, "question": "At present, the sum of the ages of a husband and wife, $P$, is six times the sum of the ages of their children, $C$. Two years ago, the sum of the ages of the husband and wife was ten times the sum of the ages of the same children. Six years from now, it will be three times the sum of the ages of the same children. Determine the number of children.", "answer": "3"}, {"index": 130, "question": "What is the value of $x$ such that $\\log _{2}\\left(\\log _{2}(2 x-2)\\right)=2$ ?", "answer": "9"}, {"index": 131, "question": "Let $f(x)=2^{k x}+9$, where $k$ is a real number. If $f(3): f(6)=1: 3$, determine the value of $f(9)-f(3)$.", "answer": "210"}, {"index": 132, "question": "Determine, with justification, all values of $k$ for which $y=x^{2}-4$ and $y=2|x|+k$ do not intersect.", "answer": "(-\\infty,-5)"}, {"index": 133, "question": "If $2 \\leq x \\leq 5$ and $10 \\leq y \\leq 20$, what is the maximum value of $15-\\frac{y}{x}$ ?", "answer": "13"}, {"index": 134, "question": "The functions $f$ and $g$ satisfy\n\n$$\n\\begin{aligned}\n& f(x)+g(x)=3 x+5 \\\\\n& f(x)-g(x)=5 x+7\n\\end{aligned}\n$$\n\nfor all values of $x$. Determine the value of $2 f(2) g(2)$.", "answer": "-84"}, {"index": 135, "question": "Three different numbers are chosen at random from the set $\\{1,2,3,4,5\\}$.\n\nThe numbers are arranged in increasing order.\n\nWhat is the probability that the resulting sequence is an arithmetic sequence?\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, 3,5,7,9 is an arithmetic sequence with four terms.)", "answer": "\\frac{2}{5}"}, {"index": 136, "question": "What is the largest two-digit number that becomes $75 \\%$ greater when its digits are reversed?", "answer": "48"}, {"index": 137, "question": "<PERSON> likes to paddle his raft down the Speed River from point $A$ to point $B$. The speed of the current in the river is always the same. When <PERSON> paddles, he always paddles at the same constant speed. On days when he paddles with the current, it takes him 18 minutes to get from $A$ to $B$. When he does not paddle, the current carries him from $A$ to $B$ in 30 minutes. If there were no current, how long would it take him to paddle from $A$ to $B$ ?", "answer": "45"}, {"index": 138, "question": "Square $O P Q R$ has vertices $O(0,0), P(0,8), Q(8,8)$, and $R(8,0)$. The parabola with equation $y=a(x-2)(x-6)$ intersects the sides of the square $O P Q R$ at points $K, L, M$, and $N$. Determine all the values of $a$ for which the area of the trapezoid $K L M N$ is 36 .", "answer": "\\frac{32}{9},\\frac{1}{2}"}, {"index": 139, "question": "A 75 year old person has a $50 \\%$ chance of living at least another 10 years.\n\nA 75 year old person has a $20 \\%$ chance of living at least another 15 years. An 80 year old person has a $25 \\%$ chance of living at least another 10 years. What is the probability that an 80 year old person will live at least another 5 years?", "answer": "62.5%"}, {"index": 140, "question": "Determine all values of $x$ for which $2^{\\log _{10}\\left(x^{2}\\right)}=3\\left(2^{1+\\log _{10} x}\\right)+16$.", "answer": "1000"}, {"index": 141, "question": "The Sieve of Sundaram uses the following infinite table of positive integers:\n\n| 4 | 7 | 10 | 13 | $\\cdots$ |\n| :---: | :---: | :---: | :---: | :---: |\n| 7 | 12 | 17 | 22 | $\\cdots$ |\n| 10 | 17 | 24 | 31 | $\\cdots$ |\n| 13 | 22 | 31 | 40 | $\\cdots$ |\n| $\\vdots$ | $\\vdots$ | $\\vdots$ | $\\vdots$ |  |\n\nThe numbers in each row in the table form an arithmetic sequence. The numbers in each column in the table form an arithmetic sequence. The first four entries in each of the first four rows and columns are shown.\nDetermine the number in the 50th row and 40th column.", "answer": "4090"}, {"index": 142, "question": "The Sieve of Sundaram uses the following infinite table of positive integers:\n\n| 4 | 7 | 10 | 13 | $\\cdots$ |\n| :---: | :---: | :---: | :---: | :---: |\n| 7 | 12 | 17 | 22 | $\\cdots$ |\n| 10 | 17 | 24 | 31 | $\\cdots$ |\n| 13 | 22 | 31 | 40 | $\\cdots$ |\n| $\\vdots$ | $\\vdots$ | $\\vdots$ | $\\vdots$ |  |\n\nThe numbers in each row in the table form an arithmetic sequence. The numbers in each column in the table form an arithmetic sequence. The first four entries in each of the first four rows and columns are shown.\nDetermine a formula for the number in the $R$ th row and $C$ th column.", "answer": "2RC+R+C"}, {"index": 143, "question": "Let $\\lfloor x\\rfloor$ denote the greatest integer less than or equal to $x$. For example, $\\lfloor 3.1\\rfloor=3$ and $\\lfloor-1.4\\rfloor=-2$.\n\nSuppose that $f(n)=2 n-\\left\\lfloor\\frac{1+\\sqrt{8 n-7}}{2}\\right\\rfloor$ and $g(n)=2 n+\\left\\lfloor\\frac{1+\\sqrt{8 n-7}}{2}\\right\\rfloor$ for each positive integer $n$.\nDetermine the value of $g(2011)$.", "answer": "4085"}, {"index": 144, "question": "Let $\\lfloor x\\rfloor$ denote the greatest integer less than or equal to $x$. For example, $\\lfloor 3.1\\rfloor=3$ and $\\lfloor-1.4\\rfloor=-2$.\n\nSuppose that $f(n)=2 n-\\left\\lfloor\\frac{1+\\sqrt{8 n-7}}{2}\\right\\rfloor$ and $g(n)=2 n+\\left\\lfloor\\frac{1+\\sqrt{8 n-7}}{2}\\right\\rfloor$ for each positive integer $n$.\nDetermine a value of $n$ for which $f(n)=100$.", "answer": "55"}, {"index": 145, "question": "Six tickets numbered 1 through 6 are placed in a box. Two tickets are randomly selected and removed together. What is the probability that the smaller of the two numbers on the tickets selected is less than or equal to 4 ?", "answer": "\\frac{14}{15}"}, {"index": 146, "question": "A goat starts at the origin $(0,0)$ and then makes several moves. On move 1 , it travels 1 unit up to $(0,1)$. On move 2 , it travels 2 units right to $(2,1)$. On move 3 , it travels 3 units down to $(2,-2)$. On move 4 , it travels 4 units to $(-2,-2)$. It continues in this fashion, so that on move $n$, it turns $90^{\\circ}$ in a clockwise direction from its previous heading and travels $n$ units in this new direction. After $n$ moves, the goat has travelled a total of 55 units. Determine the coordinates of its position at this time.", "answer": "(6,5)"}, {"index": 147, "question": "Determine all possible values of $r$ such that the three term geometric sequence 4, $4 r, 4 r^{2}$ is also an arithmetic sequence.\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, 3, 5, 7, 9, 11 is an arithmetic sequence.)", "answer": "1"}, {"index": 148, "question": "If $f(x)=\\sin ^{2} x-2 \\sin x+2$, what are the minimum and maximum values of $f(x)$ ?", "answer": "5,1"}, {"index": 149, "question": "What is the sum of the digits of the integer equal to $\\left(10^{3}+1\\right)^{2}$ ?", "answer": "1002001"}, {"index": 150, "question": "A bakery sells small and large cookies. Before a price increase, the price of each small cookie is $\\$ 1.50$ and the price of each large cookie is $\\$ 2.00$. The price of each small cookie is increased by $10 \\%$ and the price of each large cookie is increased by $5 \\%$. What is the percentage increase in the total cost of a purchase of 2 small cookies and 1 large cookie?", "answer": "8\\%"}, {"index": 151, "question": "<PERSON> is twice as old as <PERSON><PERSON>. <PERSON> is 4 years younger than <PERSON>. The average age of <PERSON>, <PERSON> and <PERSON><PERSON> is 13. Determine their ages.", "answer": "7,14,18"}, {"index": 152, "question": "The parabola with equation $y=-2 x^{2}+4 x+c$ has vertex $V(1,18)$. The parabola intersects the $y$-axis at $D$ and the $x$-axis at $E$ and $F$. Determine the area of $\\triangle D E F$.", "answer": "48"}, {"index": 153, "question": "If $3\\left(8^{x}\\right)+5\\left(8^{x}\\right)=2^{61}$, what is the value of the real number $x$ ?", "answer": "\\frac{58}{3}"}, {"index": 154, "question": "For some real numbers $m$ and $n$, the list $3 n^{2}, m^{2}, 2(n+1)^{2}$ consists of three consecutive integers written in increasing order. Determine all possible values of $m$.", "answer": "1,-1,7,-7"}, {"index": 155, "question": "<PERSON><PERSON> starts with the point $(3,5)$, and applies the following three-step process, which we call $\\mathcal{P}$ :\n\nStep 1: Reflect the point in the $x$-axis.\n\nStep 2: Translate the resulting point 2 units upwards.\n\nStep 3: Reflect the resulting point in the $y$-axis.\n\nAs she does this, the point $(3,5)$ moves to $(3,-5)$, then to $(3,-3)$, and then to $(-3,-3)$.\n\n<PERSON><PERSON> then starts with a different point $S_{0}$. She applies the three-step process $\\mathcal{P}$ to the point $S_{0}$ and obtains the point $S_{1}$. She then applies $\\mathcal{P}$ to $S_{1}$ to obtain the point $S_{2}$. She applies $\\mathcal{P}$ four more times, each time using the previous output of $\\mathcal{P}$ to be the new input, and eventually obtains the point $S_{6}(-7,-1)$. What are the coordinates of the point $S_{0}$ ?", "answer": "(-7,-1)"}, {"index": 156, "question": "Suppose that $n>5$ and that the numbers $t_{1}, t_{2}, t_{3}, \\ldots, t_{n-2}, t_{n-1}, t_{n}$ form an arithmetic sequence with $n$ terms. If $t_{3}=5, t_{n-2}=95$, and the sum of all $n$ terms is 1000 , what is the value of $n$ ?\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant, called the common difference. For example, $3,5,7,9$ are the first four terms of an arithmetic sequence.)", "answer": "20"}, {"index": 157, "question": "Suppose that $a$ and $r$ are real numbers. A geometric sequence with first term $a$ and common ratio $r$ has 4 terms. The sum of this geometric sequence is $6+6 \\sqrt{2}$. A second geometric sequence has the same first term $a$ and the same common ratio $r$, but has 8 terms. The sum of this second geometric sequence is $30+30 \\sqrt{2}$. Determine all possible values for $a$.\n\n(A geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a non-zero constant, called the common ratio. For example, $3,-6,12,-24$ are the first four terms of a geometric sequence.)", "answer": "a=2,a=-6-4\\sqrt{2}"}, {"index": 158, "question": "A bag contains 3 green balls, 4 red balls, and no other balls. <PERSON> removes balls randomly from the bag, one at a time, and places them on a table. Each ball in the bag is equally likely to be chosen each time that he removes a ball. He stops removing balls when there are two balls of the same colour on the table. What is the probability that, when he stops, there is at least 1 red ball and at least 1 green ball on the table?", "answer": "\\frac{4}{7}"}, {"index": 159, "question": "Suppose that $f(a)=2 a^{2}-3 a+1$ for all real numbers $a$ and $g(b)=\\log _{\\frac{1}{2}} b$ for all $b>0$. Determine all $\\theta$ with $0 \\leq \\theta \\leq 2 \\pi$ for which $f(g(\\sin \\theta))=0$.", "answer": "\\frac{1}{6}\\pi,\\frac{5}{6}\\pi,\\frac{1}{4}\\pi,\\frac{3}{4}\\pi"}, {"index": 160, "question": "Suppose that $a=5$ and $b=4$. Determine all pairs of integers $(K, L)$ for which $K^{2}+3 L^{2}=a^{2}+b^{2}-a b$.", "answer": "(3,2),(-3,2),(3,-2),(-3,-2)"}, {"index": 161, "question": "Determine all values of $x$ for which $0<\\frac{x^{2}-11}{x+1}<7$.", "answer": "(-\\sqrt{11},-2)\\cup(\\sqrt{11},9)"}, {"index": 162, "question": "The numbers $a_{1}, a_{2}, a_{3}, \\ldots$ form an arithmetic sequence with $a_{1} \\neq a_{2}$. The three numbers $a_{1}, a_{2}, a_{6}$ form a geometric sequence in that order. Determine all possible positive integers $k$ for which the three numbers $a_{1}, a_{4}, a_{k}$ also form a geometric sequence in that order.\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, 3, 5, 7, 9 are the first four terms of an arithmetic sequence.\n\nA geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a non-zero constant. For example, $3,6,12$ is a geometric sequence with three terms.)", "answer": "34"}, {"index": 163, "question": "For some positive integers $k$, the parabola with equation $y=\\frac{x^{2}}{k}-5$ intersects the circle with equation $x^{2}+y^{2}=25$ at exactly three distinct points $A, B$ and $C$. Determine all such positive integers $k$ for which the area of $\\triangle A B C$ is an integer.", "answer": "1,2,5,8,9"}, {"index": 164, "question": "Consider the following system of equations in which all logarithms have base 10:\n\n$$\n\\begin{aligned}\n(\\log x)(\\log y)-3 \\log 5 y-\\log 8 x & =a \\\\\n(\\log y)(\\log z)-4 \\log 5 y-\\log 16 z & =b \\\\\n(\\log z)(\\log x)-4 \\log 8 x-3 \\log 625 z & =c\n\\end{aligned}\n$$\nIf $a=-4, b=4$, and $c=-18$, solve the system of equations.", "answer": "(10^{4},10^{3},10^{10}),(10^{2},10^{-1},10^{-2})"}, {"index": 165, "question": "Two fair dice, each having six faces numbered 1 to 6 , are thrown. What is the probability that the product of the two numbers on the top faces is divisible by 5 ?", "answer": "\\frac{11}{36}"}, {"index": 166, "question": "If $f(x)=x^{2}-x+2, g(x)=a x+b$, and $f(g(x))=9 x^{2}-3 x+2$, determine all possible ordered pairs $(a, b)$ which satisfy this relationship.", "answer": "(3,0),(-3,1)"}, {"index": 167, "question": "Digital images consist of a very large number of equally spaced dots called pixels The resolution of an image is the number of pixels/cm in each of the horizontal and vertical directions.\n\nThus, an image with dimensions $10 \\mathrm{~cm}$ by $15 \\mathrm{~cm}$ and a resolution of 75 pixels/cm has a total of $(10 \\times 75) \\times(15 \\times 75)=843750$ pixels.\n\nIf each of these dimensions was increased by $n \\%$ and the resolution was decreased by $n \\%$, the image would have 345600 pixels.\n\nDetermine the value of $n$.", "answer": "60"}, {"index": 168, "question": "If $T=x^{2}+\\frac{1}{x^{2}}$, determine the values of $b$ and $c$ so that $x^{6}+\\frac{1}{x^{6}}=T^{3}+b T+c$ for all non-zero real numbers $x$.", "answer": "-3,0"}, {"index": 169, "question": "A Skolem sequence of order $n$ is a sequence $\\left(s_{1}, s_{2}, \\ldots, s_{2 n}\\right)$ of $2 n$ integers satisfying the conditions:\n\ni) for every $k$ in $\\{1,2,3, \\ldots, n\\}$, there exist exactly two elements $s_{i}$ and $s_{j}$ with $s_{i}=s_{j}=k$, and\n\nii) if $s_{i}=s_{j}=k$ with $i<j$, then $j-i=k$.\n\n\nFor example, $(4,2,3,2,4,3,1,1)$ is a Skolem sequence of order 4.\nList all Skolem sequences of order 4.", "answer": "(4,2,3,2,4,3,1,1),(1,1,3,4,2,3,2,4),(4,1,1,3,4,2,3,2),(2,3,2,4,3,1,1,4),(3,4,2,3,2,4,1,1),(1,1,4,2,3,2,4,3)"}, {"index": 170, "question": "A Skolem sequence of order $n$ is a sequence $\\left(s_{1}, s_{2}, \\ldots, s_{2 n}\\right)$ of $2 n$ integers satisfying the conditions:\n\ni) for every $k$ in $\\{1,2,3, \\ldots, n\\}$, there exist exactly two elements $s_{i}$ and $s_{j}$ with $s_{i}=s_{j}=k$, and\n\nii) if $s_{i}=s_{j}=k$ with $i<j$, then $j-i=k$.\n\n\nFor example, $(4,2,3,2,4,3,1,1)$ is a Skolem sequence of order 4.\nDetermine, with justification, all Skolem sequences of order 9 which satisfy all of the following three conditions:\nI) $s_{3}=1$,\n\nII) $s_{18}=8$, and\n\nIII) between any two equal even integers, there is exactly one odd integer.", "answer": "(7,5,1,1,9,3,5,7,3,8,6,4,2,9,2,4,6,8)"}, {"index": 171, "question": "The three-digit positive integer $m$ is odd and has three distinct digits. If the hundreds digit of $m$ equals the product of the tens digit and ones (units) digit of $m$, what is $m$ ?", "answer": "623"}, {"index": 172, "question": "<PERSON> has 100 marbles, each of which is black or gold. The ratio of the number of black marbles to the number of gold marbles is $1: 4$. How many gold marbles should she add to change this ratio to $1: 6$ ?", "answer": "40"}, {"index": 173, "question": "Suppose that $n$ is a positive integer and that the value of $\\frac{n^{2}+n+15}{n}$ is an integer. Determine all possible values of $n$.", "answer": "1,3,5,15"}, {"index": 174, "question": "<PERSON> starts with $x=10$ and $y=2$, and applies the following process:\n\nStep 1: Add $x$ and $y$. Let $x$ equal the result. The value of $y$ does not change. Step 2: Multiply $x$ and $y$. Let $x$ equal the result. The value of $y$ does not change.\n\nStep 3: Add $y$ and 1. Let $y$ equal the result. The value of $x$ does not change.\n\n<PERSON> keeps track of the values of $x$ and $y$ :\n\n|  | $x$ | $y$ |\n| :---: | :---: | :---: |\n| Before Step 1 | 10 | 2 |\n| After Step 1 | 12 | 2 |\n| After Step 2 | 24 | 2 |\n| After Step 3 | 24 | 3 |\n\nContinuing now with $x=24$ and $y=3$, <PERSON> applies the process two more times. What is the final value of $x$ ?", "answer": "340"}, {"index": 175, "question": "Determine all integers $k$, with $k \\neq 0$, for which the parabola with equation $y=k x^{2}+6 x+k$ has two distinct $x$-intercepts.", "answer": "-2,-1,1,2"}, {"index": 176, "question": "The positive integers $a$ and $b$ have no common divisor larger than 1 . If the difference between $b$ and $a$ is 15 and $\\frac{5}{9}<\\frac{a}{b}<\\frac{4}{7}$, what is the value of $\\frac{a}{b}$ ?", "answer": "\\frac{19}{34}"}, {"index": 177, "question": "A geometric sequence has first term 10 and common ratio $\\frac{1}{2}$.\n\nAn arithmetic sequence has first term 10 and common difference $d$.\n\nThe ratio of the 6th term in the geometric sequence to the 4th term in the geometric sequence equals the ratio of the 6th term in the arithmetic sequence to the 4 th term in the arithmetic sequence.\n\nDetermine all possible values of $d$.\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant, called the common difference. For example, 3, 5, 7, 9 are the first four terms of an arithmetic sequence.\n\nA geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a non-zero constant, called the common ratio. For example, $3,6,12$ is a geometric sequence with three terms.)", "answer": "-\\frac{30}{17}"}, {"index": 178, "question": "For each positive real number $x$, define $f(x)$ to be the number of prime numbers $p$ that satisfy $x \\leq p \\leq x+10$. What is the value of $f(f(20))$ ?", "answer": "5"}, {"index": 179, "question": "Determine all triples $(x, y, z)$ of real numbers that satisfy the following system of equations:\n\n$$\n\\begin{aligned}\n(x-1)(y-2) & =0 \\\\\n(x-3)(z+2) & =0 \\\\\nx+y z & =9\n\\end{aligned}\n$$", "answer": "(1,-4,-2),(3,2,3),(13,2,-2)"}, {"index": 180, "question": "Suppose that the function $g$ satisfies $g(x)=2 x-4$ for all real numbers $x$ and that $g^{-1}$ is the inverse function of $g$. Suppose that the function $f$ satisfies $g\\left(f\\left(g^{-1}(x)\\right)\\right)=2 x^{2}+16 x+26$ for all real numbers $x$. What is the value of $f(\\pi)$ ?", "answer": "4\\pi^{2}-1"}, {"index": 181, "question": "Determine all pairs of angles $(x, y)$ with $0^{\\circ} \\leq x<180^{\\circ}$ and $0^{\\circ} \\leq y<180^{\\circ}$ that satisfy the following system of equations:\n\n$$\n\\begin{aligned}\n\\log _{2}(\\sin x \\cos y) & =-\\frac{3}{2} \\\\\n\\log _{2}\\left(\\frac{\\sin x}{\\cos y}\\right) & =\\frac{1}{2}\n\\end{aligned}\n$$", "answer": "(45^{\\circ},60^{\\circ}),(135^{\\circ},60^{\\circ})"}, {"index": 182, "question": "Four tennis players <PERSON>, <PERSON>, <PERSON>, and <PERSON> take part in a tournament in which a total of three matches are played. First, two players are chosen randomly to play each other. The other two players also play each other. The winners of the two matches then play to decide the tournament champion. <PERSON>, <PERSON> and <PERSON> are equally matched (that is, when a match is played between any two of them, the probability that each player wins is $\\frac{1}{2}$ ). When <PERSON> plays each of <PERSON>, <PERSON> and <PERSON>, the probability that <PERSON> wins is $p$, for some real number $p$. Determine the probability that <PERSON> wins the tournament, expressing your answer in the form $\\frac{a p^{2}+b p+c}{d}$ where $a, b, c$, and $d$ are integers.", "answer": "\\frac{1-p^{2}}{3}"}, {"index": 183, "question": "Three microphones $A, B$ and $C$ are placed on a line such that $A$ is $1 \\mathrm{~km}$ west of $B$ and $C$ is $2 \\mathrm{~km}$ east of $B$. A large explosion occurs at a point $P$ not on this line. Each of the three microphones receives the sound. The sound travels at $\\frac{1}{3} \\mathrm{~km} / \\mathrm{s}$. Microphone $B$ receives the sound first, microphone $A$ receives the sound $\\frac{1}{2}$ s later, and microphone $C$ receives it $1 \\mathrm{~s}$ after microphone $A$. Determine the distance from microphone $B$ to the explosion at $P$.", "answer": "\\frac{41}{12}"}, {"index": 184, "question": "<PERSON> has a list of $n$ integers $a_{1}, a_{2}, \\ldots, a_{n}$ satisfying $a_{1} \\leq a_{2} \\leq \\ldots \\leq a_{n}$. <PERSON> calculates the pairwise sums of all $m=\\frac{1}{2} n(n-1)$ possible pairs of integers in her list and orders these pairwise sums as $s_{1} \\leq s_{2} \\leq \\ldots \\leq s_{m}$. For example, if <PERSON>'s list consists of the three integers $1,2,4$, the three pairwise sums are $3,5,6$.\n\n\nSuppose that $n=4$ and that the 6 pairwise sums are $s_{1}=8, s_{2}=104, s_{3}=106$, $s_{4}=110, s_{5}=112$, and $s_{6}=208$. Determine two possible lists $(a_{1}, a_{2}, a_{3}, a_{4})$ that <PERSON> could have.", "answer": "(1,7,103,105),(3,5,101,107)"}, {"index": 185, "question": "Determine all values of $x$ for which $\\frac{x^{2}+x+4}{2 x+1}=\\frac{4}{x}$.", "answer": "-1,2,-2"}, {"index": 186, "question": "Determine the number of positive divisors of 900, including 1 and 900, that are perfect squares. (A positive divisor of 900 is a positive integer that divides exactly into 900.)", "answer": "8"}, {"index": 187, "question": "Points $A(k, 3), B(3,1)$ and $C(6, k)$ form an isosceles triangle. If $\\angle A B C=\\angle A C B$, determine all possible values of $k$.", "answer": "8,4"}, {"index": 188, "question": "A chemist has three bottles, each containing a mixture of acid and water:\n\n- bottle A contains $40 \\mathrm{~g}$ of which $10 \\%$ is acid,\n- bottle B contains $50 \\mathrm{~g}$ of which $20 \\%$ is acid, and\n- bottle C contains $50 \\mathrm{~g}$ of which $30 \\%$ is acid.\n\nShe uses some of the mixture from each of the bottles to create a mixture with mass $60 \\mathrm{~g}$ of which $25 \\%$ is acid. Then she mixes the remaining contents of the bottles to create a new mixture. What percentage of the new mixture is acid?", "answer": "17.5%"}, {"index": 189, "question": "Suppose that $x$ and $y$ are real numbers with $3 x+4 y=10$. Determine the minimum possible value of $x^{2}+16 y^{2}$.", "answer": "10"}, {"index": 190, "question": "A bag contains 40 balls, each of which is black or gold. <PERSON><PERSON><PERSON> reaches into the bag and randomly removes two balls. Each ball in the bag is equally likely to be removed. If the probability that two gold balls are removed is $\\frac{5}{12}$, how many of the 40 balls are gold?", "answer": "26"}, {"index": 191, "question": "The geometric sequence with $n$ terms $t_{1}, t_{2}, \\ldots, t_{n-1}, t_{n}$ has $t_{1} t_{n}=3$. Also, the product of all $n$ terms equals 59049 (that is, $t_{1} t_{2} \\cdots t_{n-1} t_{n}=59049$ ). Determine the value of $n$.\n\n(A geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a constant. For example, $3,6,12$ is a geometric sequence with three terms.)", "answer": "20"}, {"index": 192, "question": "If $\\frac{(x-2013)(y-2014)}{(x-2013)^{2}+(y-2014)^{2}}=-\\frac{1}{2}$, what is the value of $x+y$ ?", "answer": "4027"}, {"index": 193, "question": "Determine all real numbers $x$ for which\n\n$$\n\\left(\\log _{10} x\\right)^{\\log _{10}\\left(\\log _{10} x\\right)}=10000\n$$", "answer": "10^{100},10^{1/100}"}, {"index": 194, "question": "Without using a calculator, determine positive integers $m$ and $n$ for which\n\n$$\n\\sin ^{6} 1^{\\circ}+\\sin ^{6} 2^{\\circ}+\\sin ^{6} 3^{\\circ}+\\cdots+\\sin ^{6} 87^{\\circ}+\\sin ^{6} 88^{\\circ}+\\sin ^{6} 89^{\\circ}=\\frac{m}{n}\n$$\n\n(The sum on the left side of the equation consists of 89 terms of the form $\\sin ^{6} x^{\\circ}$, where $x$ takes each positive integer value from 1 to 89.)", "answer": "221,8"}, {"index": 195, "question": "Let $f(n)$ be the number of positive integers that have exactly $n$ digits and whose digits have a sum of 5. Determine, with proof, how many of the 2014 integers $f(1), f(2), \\ldots, f(2014)$ have a units digit of 1 .", "answer": "202"}, {"index": 196, "question": "If $\\log _{10} x=3+\\log _{10} y$, what is the value of $\\frac{x}{y}$ ?", "answer": "1000"}, {"index": 197, "question": "If $x+\\frac{1}{x}=\\frac{13}{6}$, determine all values of $x^{2}+\\frac{1}{x^{2}}$.", "answer": "\\frac{97}{36}"}, {"index": 198, "question": "A die, with the numbers $1,2,3,4,6$, and 8 on its six faces, is rolled. After this roll, if an odd number appears on the top face, all odd numbers on the die are doubled. If an even number appears on the top face, all the even numbers are halved. If the given die changes in this way, what is the probability that a 2 will appear on the second roll of the die?", "answer": "\\frac{2}{9}"}, {"index": 199, "question": "The table below gives the final standings for seven of the teams in the English Cricket League in 1998. At the end of the year, each team had played 17 matches and had obtained the total number of points shown in the last column. Each win $W$, each draw $D$, each bonus bowling point $A$, and each bonus batting point $B$ received $w, d, a$ and $b$ points respectively, where $w, d, a$ and $b$ are positive integers. No points are given for a loss. Determine the values of $w, d, a$ and $b$ if total points awarded are given by the formula: Points $=w \\times W+d \\times D+a \\times A+b \\times B$.\n\n\n\nFinal Standings\n\n|  | $W$ | Losses | $D$ | $A$ | $B$ | Points |\n| :--- | :---: | :---: | :---: | :---: | :---: | :---: |\n| Sussex | 6 | 7 | 4 | 30 | 63 | 201 |\n| Warks | 6 | 8 | 3 | 35 | 60 | 200 |\n| Som | 6 | 7 | 4 | 30 | 54 | 192 |\n| Derbys | 6 | 7 | 4 | 28 | 55 | 191 |\n| Kent | 5 | 5 | 7 | 18 | 59 | 178 |\n| Worcs | 4 | 6 | 7 | 32 | 59 | 176 |\n| Glam | 4 | 6 | 7 | 36 | 55 | 176 |", "answer": "16,3,1,1"}, {"index": 200, "question": "Let $\\lfloor x\\rfloor$ represent the greatest integer which is less than or equal to $x$. For example, $\\lfloor 3\\rfloor=3,\\lfloor 2.6\\rfloor=2$. If $x$ is positive and $x\\lfloor x\\rfloor=17$, what is the value of $x$ ?", "answer": "4.25"}, {"index": 201, "question": "A cube has edges of length $n$, where $n$ is an integer. Three faces, meeting at a corner, are painted red. The cube is then cut into $n^{3}$ smaller cubes of unit length. If exactly 125 of these cubes have no faces painted red, determine the value of $n$.", "answer": "6"}, {"index": 202, "question": "<PERSON><PERSON><PERSON> bought some stuffed goats and some toy helicopters. She paid a total of $\\$ 201$. She did not buy partial goats or partial helicopters. Each stuffed goat cost $\\$ 19$ and each toy helicopter cost $\\$ 17$. How many of each did she buy?", "answer": "7,4"}, {"index": 203, "question": "Determine all real values of $x$ for which $(x+8)^{4}=(2 x+16)^{2}$.", "answer": "-6,-8,-10"}, {"index": 204, "question": "If $f(x)=2 x+1$ and $g(f(x))=4 x^{2}+1$, determine an expression for $g(x)$.", "answer": "g(x)=x^2-2x+2"}, {"index": 205, "question": "A geometric sequence has 20 terms.\n\nThe sum of its first two terms is 40 .\n\nThe sum of its first three terms is 76 .\n\nThe sum of its first four terms is 130 .\n\nDetermine how many of the terms in the sequence are integers.\n\n(A geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a constant. For example, $3,6,12$ is a geometric sequence with three terms.)", "answer": "5"}, {"index": 206, "question": "Determine all real values of $x$ for which $3^{(x-1)} 9^{\\frac{3}{2 x^{2}}}=27$.", "answer": "1,\\frac{3+\\sqrt{21}}{2},\\frac{3-\\sqrt{21}}{2}"}, {"index": 207, "question": "Determine all points $(x, y)$ where the two curves $y=\\log _{10}\\left(x^{4}\\right)$ and $y=\\left(\\log _{10} x\\right)^{3}$ intersect.", "answer": "(1,0),(\\frac{1}{100},-8),(100,8)"}, {"index": 208, "question": "<PERSON><PERSON><PERSON><PERSON> tosses three fair coins and removes all of the coins that come up heads. <PERSON> then tosses the coins that remain, if any. Determine the probability that <PERSON> tosses exactly one head.", "answer": "\\frac{27}{64}"}, {"index": 209, "question": "<PERSON> starts with an angle of measure $8^{\\circ}$ and doubles it 10 times until he obtains $8192^{\\circ}$. He then adds up the reciprocals of the sines of these 11 angles. That is, he calculates\n\n$$\nS=\\frac{1}{\\sin 8^{\\circ}}+\\frac{1}{\\sin 16^{\\circ}}+\\frac{1}{\\sin 32^{\\circ}}+\\cdots+\\frac{1}{\\sin 4096^{\\circ}}+\\frac{1}{\\sin 8192^{\\circ}}\n$$\n\nDetermine, without using a calculator, the measure of the acute angle $\\alpha$ so that $S=\\frac{1}{\\sin \\alpha}$.", "answer": "4^{\\circ}"}, {"index": 210, "question": "For each positive integer $n$, let $T(n)$ be the number of triangles with integer side lengths, positive area, and perimeter $n$. For example, $T(6)=1$ since the only such triangle with a perimeter of 6 has side lengths 2,2 and 2 .\nDetermine the values of $T(10), T(11)$ and $T(12)$.", "answer": "2,4,3"}, {"index": 211, "question": "For each positive integer $n$, let $T(n)$ be the number of triangles with integer side lengths, positive area, and perimeter $n$. For example, $T(6)=1$ since the only such triangle with a perimeter of 6 has side lengths 2,2 and 2 .\nDetermine the smallest positive integer $n$ such that $T(n)>2010$.", "answer": "309"}, {"index": 212, "question": "Suppose $0^{\\circ}<x<90^{\\circ}$ and $2 \\sin ^{2} x+\\cos ^{2} x=\\frac{25}{16}$. What is the value of $\\sin x$ ?", "answer": "\\frac{3}{4}"}, {"index": 213, "question": "The first term of a sequence is 2007. Each term, starting with the second, is the sum of the cubes of the digits of the previous term. What is the 2007th term?", "answer": "153"}, {"index": 214, "question": "Sequence A has $n$th term $n^{2}-10 n+70$.\n\n(The first three terms of sequence $\\mathrm{A}$ are $61,54,49$. )\n\nSequence B is an arithmetic sequence with first term 5 and common difference 10. (The first three terms of sequence $\\mathrm{B}$ are $5,15,25$.)\n\nDetermine all $n$ for which the $n$th term of sequence $\\mathrm{A}$ is equal to the $n$th term of sequence B. Explain how you got your answer.", "answer": "5,15"}, {"index": 215, "question": "Determine all values of $x$ for which $2+\\sqrt{x-2}=x-2$.", "answer": "6"}, {"index": 216, "question": "Determine all values of $x$ for which $(\\sqrt{x})^{\\log _{10} x}=100$.", "answer": "100,\\frac{1}{100}"}, {"index": 217, "question": "Suppose that $f(x)=x^{2}+(2 n-1) x+\\left(n^{2}-22\\right)$ for some integer $n$. What is the smallest positive integer $n$ for which $f(x)$ has no real roots?", "answer": "23"}, {"index": 218, "question": "A bag contains 3 red marbles and 6 blue marbles. <PERSON><PERSON><PERSON> removes one marble at a time until the bag is empty. Each marble that they remove is chosen randomly from the remaining marbles. Given that the first marble that <PERSON><PERSON><PERSON> removes is red and the third marble that they remove is blue, what is the probability that the last two marbles that <PERSON><PERSON><PERSON> removes are both blue?", "answer": "\\frac{10}{21}"}, {"index": 219, "question": "Determine the number of quadruples of positive integers $(a, b, c, d)$ with $a<b<c<d$ that satisfy both of the following system of equations:\n\n$$\n\\begin{aligned}\na c+a d+b c+b d & =2023 \\\\\na+b+c+d & =296\n\\end{aligned}\n$$", "answer": "417"}, {"index": 220, "question": "Suppose that $\\triangle A B C$ is right-angled at $B$ and has $A B=n(n+1)$ and $A C=(n+1)(n+4)$, where $n$ is a positive integer. Determine the number of positive integers $n<100000$ for which the length of side $B C$ is also an integer.", "answer": "222"}, {"index": 221, "question": "Determine all real values of $x$ for which\n\n$$\n\\sqrt{\\log _{2} x \\cdot \\log _{2}(4 x)+1}+\\sqrt{\\log _{2} x \\cdot \\log _{2}\\left(\\frac{x}{64}\\right)+9}=4\n$$", "answer": "[\\frac{1}{2},8]"}, {"index": 222, "question": "For every real number $x$, define $\\lfloor x\\rfloor$ to be equal to the greatest integer less than or equal to $x$. (We call this the \"floor\" of $x$.) For example, $\\lfloor 4.2\\rfloor=4,\\lfloor 5.7\\rfloor=5$, $\\lfloor-3.4\\rfloor=-4,\\lfloor 0.4\\rfloor=0$, and $\\lfloor 2\\rfloor=2$.\nDetermine the integer equal to $\\left\\lfloor\\frac{1}{3}\\right\\rfloor+\\left\\lfloor\\frac{2}{3}\\right\\rfloor+\\left\\lfloor\\frac{3}{3}\\right\\rfloor+\\ldots+\\left\\lfloor\\frac{59}{3}\\right\\rfloor+\\left\\lfloor\\frac{60}{3}\\right\\rfloor$. (The sum has 60 terms.)", "answer": "590"}, {"index": 223, "question": "For every real number $x$, define $\\lfloor x\\rfloor$ to be equal to the greatest integer less than or equal to $x$. (We call this the \"floor\" of $x$.) For example, $\\lfloor 4.2\\rfloor=4,\\lfloor 5.7\\rfloor=5$, $\\lfloor-3.4\\rfloor=-4,\\lfloor 0.4\\rfloor=0$, and $\\lfloor 2\\rfloor=2$.\nDetermine a polynomial $p(x)$ so that for every positive integer $m>4$,\n\n$$\n\\lfloor p(m)\\rfloor=\\left\\lfloor\\frac{1}{3}\\right\\rfloor+\\left\\lfloor\\frac{2}{3}\\right\\rfloor+\\left\\lfloor\\frac{3}{3}\\right\\rfloor+\\ldots+\\left\\lfloor\\frac{m-2}{3}\\right\\rfloor+\\left\\lfloor\\frac{m-1}{3}\\right\\rfloor\n$$\n\n(The sum has $m-1$ terms.)\n\nA polynomial $f(x)$ is an algebraic expression of the form $f(x)=a_{n} x^{n}+a_{n-1} x^{n-1}+\\cdots+a_{1} x+a_{0}$ for some integer $n \\geq 0$ and for some real numbers $a_{n}, a_{n-1}, \\ldots, a_{1}, a_{0}$.", "answer": "p(x)=\\frac{(x-1)(x-2)}{6}"}, {"index": 224, "question": "One of the faces of a rectangular prism has area $27 \\mathrm{~cm}^{2}$. Another face has area $32 \\mathrm{~cm}^{2}$. If the volume of the prism is $144 \\mathrm{~cm}^{3}$, determine the surface area of the prism in $\\mathrm{cm}^{2}$.", "answer": "166"}, {"index": 225, "question": "The equations $y=a(x-2)(x+4)$ and $y=2(x-h)^{2}+k$ represent the same parabola. What are the values of $a, h$ and $k$ ?", "answer": "2,-1,-18"}, {"index": 226, "question": "In an arithmetic sequence with 5 terms, the sum of the squares of the first 3 terms equals the sum of the squares of the last 2 terms. If the first term is 5 , determine all possible values of the fifth term.\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, 3,5,7,9,11 is an arithmetic sequence with five terms.)", "answer": "-5,7"}, {"index": 227, "question": "<PERSON> was born in a year between 1300 and 1400. <PERSON> was born in a year between 1400 and 1500. Each was born on April 6 in a year that is a perfect square. Each lived for 110 years. In what year while they were both alive were their ages both perfect squares on April 7?", "answer": "1469"}, {"index": 228, "question": "Determine all values of $k$ for which the points $A(1,2), B(11,2)$ and $C(k, 6)$ form a right-angled triangle.", "answer": "1,3,9,11"}, {"index": 229, "question": "If $\\cos \\theta=\\tan \\theta$, determine all possible values of $\\sin \\theta$, giving your answer(s) as simplified exact numbers.", "answer": "\\frac{-1+\\sqrt{5}}{2}"}, {"index": 230, "question": "<PERSON><PERSON> is driving at $60 \\mathrm{~km} / \\mathrm{h}$ on a long straight highway parallel to a train track. Every 10 minutes, she is passed by a train travelling in the same direction as she is. These trains depart from the station behind her every 3 minutes and all travel at the same constant speed. What is the constant speed of the trains, in $\\mathrm{km} / \\mathrm{h}$ ?", "answer": "\\frac{600}{7}"}, {"index": 231, "question": "Determine all pairs $(a, b)$ of real numbers that satisfy the following system of equations:\n\n$$\n\\begin{aligned}\n\\sqrt{a}+\\sqrt{b} & =8 \\\\\n\\log _{10} a+\\log _{10} b & =2\n\\end{aligned}\n$$\n\nGive your answer(s) as pairs of simplified exact numbers.", "answer": "(22+8\\sqrt{6},22-8\\sqrt{6}),(22-8\\sqrt{6},22+8\\sqrt{6})"}, {"index": 232, "question": "A permutation of a list of numbers is an ordered arrangement of the numbers in that list. For example, $3,2,4,1,6,5$ is a permutation of $1,2,3,4,5,6$. We can write this permutation as $a_{1}, a_{2}, a_{3}, a_{4}, a_{5}, a_{6}$, where $a_{1}=3, a_{2}=2, a_{3}=4, a_{4}=1, a_{5}=6$, and $a_{6}=5$.\nDetermine the average value of\n\n$$\n\\left|a_{1}-a_{2}\\right|+\\left|a_{3}-a_{4}\\right|\n$$\n\nover all permutations $a_{1}, a_{2}, a_{3}, a_{4}$ of $1,2,3,4$.", "answer": "\\frac{10}{3}"}, {"index": 233, "question": "A permutation of a list of numbers is an ordered arrangement of the numbers in that list. For example, $3,2,4,1,6,5$ is a permutation of $1,2,3,4,5,6$. We can write this permutation as $a_{1}, a_{2}, a_{3}, a_{4}, a_{5}, a_{6}$, where $a_{1}=3, a_{2}=2, a_{3}=4, a_{4}=1, a_{5}=6$, and $a_{6}=5$.\nDetermine the average value of\n\n$$\na_{1}-a_{2}+a_{3}-a_{4}+a_{5}-a_{6}+a_{7}\n$$\n\nover all permutations $a_{1}, a_{2}, a_{3}, a_{4}, a_{5}, a_{6}, a_{7}$ of $1,2,3,4,5,6,7$.", "answer": "4"}, {"index": 234, "question": "A permutation of a list of numbers is an ordered arrangement of the numbers in that list. For example, $3,2,4,1,6,5$ is a permutation of $1,2,3,4,5,6$. We can write this permutation as $a_{1}, a_{2}, a_{3}, a_{4}, a_{5}, a_{6}$, where $a_{1}=3, a_{2}=2, a_{3}=4, a_{4}=1, a_{5}=6$, and $a_{6}=5$.\nDetermine the average value of\n\n$$\n\\left|a_{1}-a_{2}\\right|+\\left|a_{3}-a_{4}\\right|+\\cdots+\\left|a_{197}-a_{198}\\right|+\\left|a_{199}-a_{200}\\right|\n$$\n\nover all permutations $a_{1}, a_{2}, a_{3}, \\ldots, a_{199}, a_{200}$ of $1,2,3,4, \\ldots, 199,200$. (The sum labelled (*) contains 100 terms of the form $\\left|a_{2 k-1}-a_{2 k}\\right|$.)", "answer": "6700"}, {"index": 235, "question": "If $0^{\\circ}<x<90^{\\circ}$ and $3 \\sin (x)-\\cos \\left(15^{\\circ}\\right)=0$, what is the value of $x$ to the nearest tenth of a degree?", "answer": "18.8^{\\circ}"}, {"index": 236, "question": "The function $f(x)$ has the property that $f(2 x+3)=2 f(x)+3$ for all $x$. If $f(0)=6$, what is the value of $f(9)$ ?", "answer": "33"}, {"index": 237, "question": "Suppose that the functions $f(x)$ and $g(x)$ satisfy the system of equations\n\n$$\n\\begin{aligned}\nf(x)+3 g(x) & =x^{2}+x+6 \\\\\n2 f(x)+4 g(x) & =2 x^{2}+4\n\\end{aligned}\n$$\n\nfor all $x$. Determine the values of $x$ for which $f(x)=g(x)$.", "answer": "5,-2"}, {"index": 238, "question": "In a short-track speed skating event, there are five finalists including two Canadians. The first three skaters to finish the race win a medal. If all finalists have the same chance of finishing in any position, what is the probability that neither Canadian wins a medal?", "answer": "\\frac{1}{10}"}, {"index": 239, "question": "Determine the number of positive integers less than or equal to 300 that are multiples of 3 or 5 , but are not multiples of 10 or 15 .", "answer": "100"}, {"index": 240, "question": "In the series of odd numbers $1+3+5-7-9-11+13+15+17-19-21-23 \\ldots$ the signs alternate every three terms, as shown. What is the sum of the first 300 terms of the series?", "answer": "-900"}, {"index": 241, "question": "A two-digit number has the property that the square of its tens digit plus ten times its units digit equals the square of its units digit plus ten times its tens digit. Determine all two-digit numbers which have this property, and are prime numbers.", "answer": "11,19,37,73"}, {"index": 242, "question": "A lead box contains samples of two radioactive isotopes of iron. Isotope A decays so that after every 6 minutes, the number of atoms remaining is halved. Initially, there are twice as many atoms of isotope $\\mathrm{A}$ as of isotope $\\mathrm{B}$, and after 24 minutes there are the same number of atoms of each isotope. How long does it take the number of atoms of isotope B to halve?", "answer": "8"}, {"index": 243, "question": "Solve the system of equations:\n\n$$\n\\begin{aligned}\n& \\log _{10}\\left(x^{3}\\right)+\\log _{10}\\left(y^{2}\\right)=11 \\\\\n& \\log _{10}\\left(x^{2}\\right)-\\log _{10}\\left(y^{3}\\right)=3\n\\end{aligned}\n$$", "answer": "10^{3},10"}, {"index": 244, "question": "A positive integer $n$ is called \"savage\" if the integers $\\{1,2,\\dots,n\\}$ can be partitioned into three sets $A, B$ and $C$ such that\n\ni) the sum of the elements in each of $A, B$, and $C$ is the same,\n\nii) $A$ contains only odd numbers,\n\niii) $B$ contains only even numbers, and\n\niv) C contains every multiple of 3 (and possibly other numbers).\nDetermine all even savage integers less than 100.", "answer": "8,32,44,68,80"}, {"index": 245, "question": "<PERSON> has two identical dice. Each die has six faces which are numbered 2, 3, 5, $7,11,13$. When <PERSON> rolls the two dice, what is the probability that the sum of the numbers on the top faces is a prime number?", "answer": "\\frac{1}{6}"}, {"index": 246, "question": "If $\\frac{1}{\\cos x}-\\tan x=3$, what is the numerical value of $\\sin x$ ?", "answer": "-\\frac{4}{5}"}, {"index": 247, "question": "Determine all linear functions $f(x)=a x+b$ such that if $g(x)=f^{-1}(x)$ for all values of $x$, then $f(x)-g(x)=44$ for all values of $x$. (Note: $f^{-1}$ is the inverse function of $f$.)", "answer": "f(x)=x+22"}, {"index": 248, "question": "Determine all pairs $(a, b)$ of positive integers for which $a^{3}+2 a b=2013$.", "answer": "(1,1006),(3,331),(11,31)"}, {"index": 249, "question": "Determine all real values of $x$ for which $\\log _{2}\\left(2^{x-1}+3^{x+1}\\right)=2 x-\\log _{2}\\left(3^{x}\\right)$.", "answer": "\\frac{\\log2}{\\log2-\\log3}"}, {"index": 250, "question": "A multiplicative partition of a positive integer $n \\geq 2$ is a way of writing $n$ as a product of one or more integers, each greater than 1. Note that we consider a positive integer to be a multiplicative partition of itself. Also, the order of the factors in a partition does not matter; for example, $2 \\times 3 \\times 5$ and $2 \\times 5 \\times 3$ are considered to be the same partition of 30 . For each positive integer $n \\geq 2$, define $P(n)$ to be the number of multiplicative partitions of $n$. We also define $P(1)=1$. Note that $P(40)=7$, since the multiplicative partitions of 40 are $40,2 \\times 20,4 \\times 10$, $5 \\times 8,2 \\times 2 \\times 10,2 \\times 4 \\times 5$, and $2 \\times 2 \\times 2 \\times 5$.\n\n(In each part, we use \"partition\" to mean \"multiplicative partition\". We also call the numbers being multiplied together in a given partition the \"parts\" of the partition.)\nDetermine the value of $P(64)$.", "answer": "11"}, {"index": 251, "question": "A multiplicative partition of a positive integer $n \\geq 2$ is a way of writing $n$ as a product of one or more integers, each greater than 1. Note that we consider a positive integer to be a multiplicative partition of itself. Also, the order of the factors in a partition does not matter; for example, $2 \\times 3 \\times 5$ and $2 \\times 5 \\times 3$ are considered to be the same partition of 30 . For each positive integer $n \\geq 2$, define $P(n)$ to be the number of multiplicative partitions of $n$. We also define $P(1)=1$. Note that $P(40)=7$, since the multiplicative partitions of 40 are $40,2 \\times 20,4 \\times 10$, $5 \\times 8,2 \\times 2 \\times 10,2 \\times 4 \\times 5$, and $2 \\times 2 \\times 2 \\times 5$.\n\n(In each part, we use \"partition\" to mean \"multiplicative partition\". We also call the numbers being multiplied together in a given partition the \"parts\" of the partition.)\nDetermine the value of $P(1000)$.", "answer": "31"}, {"index": 252, "question": "What are all values of $x$ such that\n\n$$\n\\log _{5}(x+3)+\\log _{5}(x-1)=1 ?\n$$", "answer": "2"}, {"index": 253, "question": "A chef aboard a luxury liner wants to cook a goose. The time $t$ in hours to cook a goose at $180^{\\circ} \\mathrm{C}$ depends on the mass of the goose $m$ in kilograms according to the formula\n\n$$\nt=a m^{b}\n$$\n\nwhere $a$ and $b$ are constants. The table below gives the times observed to cook a goose at $180^{\\circ} \\mathrm{C}$.\n\n| Mass, $m(\\mathrm{~kg})$ | Time, $t(\\mathrm{~h})$ |\n| :---: | :---: |\n| 3.00 | 2.75 |\n| 6.00 | 3.75 |\nUsing the data in the table, determine both $a$ and $b$ to two decimal places.", "answer": "1.68,0.45"}, {"index": 254, "question": "A circle passes through the origin and the points of intersection of the parabolas $y=x^{2}-3$ and $y=-x^{2}-2 x+9$. Determine the coordinates of the centre of this circle.", "answer": "(-\\frac{1}{2},\\frac{7}{2})"}, {"index": 255, "question": "In a soccer league with 5 teams, each team plays 20 games(that is, 5 games with each of the other 4 teams). For each team, every game ends in a win (W), a loss (L), or a tie (T). The numbers of wins, losses and ties for each team at the end of the season are shown in the table. Determine the values of $x, y$ and $z$.\n\n| Team | W | L | T |\n| :---: | ---: | ---: | ---: |\n| A | 2 | 15 | 3 |\n| B | 7 | 9 | 4 |\n| C | 6 | 12 | 2 |\n| D | 10 | 8 | 2 |\n| E | $x$ | $y$ | $z$ |", "answer": "19,0,1"}, {"index": 256, "question": "Three thin metal rods of lengths 9,12 and 15 are welded together to form a right-angled triangle, which is held in a horizontal position. A solid sphere of radius 5 rests in the triangle so that it is tangent to each of the three sides. Assuming that the thickness of the rods can be neglected, how high above the plane of the triangle is the top of the sphere?", "answer": "5"}, {"index": 257, "question": "Triangle $A B C$ has vertices $A(0,5), B(3,0)$ and $C(8,3)$. Determine the measure of $\\angle A C B$.", "answer": "45^{\\circ}"}, {"index": 258, "question": "<PERSON><PERSON><PERSON> and <PERSON> will play 6 games of squash. Since they are equally skilled, each is equally likely to win any given game. (In squash, there are no ties.) The probability that each of them will win 3 of the 6 games is $\\frac{5}{16}$. What is the probability that <PERSON><PERSON><PERSON> will win more games than <PERSON>?", "answer": "\\frac{11}{32}"}, {"index": 259, "question": "Determine all real values of $x$ for which\n\n$$\n3^{x+2}+2^{x+2}+2^{x}=2^{x+5}+3^{x}\n$$", "answer": "3"}, {"index": 260, "question": "Determine all real values of $x$ such that\n\n$$\n\\log _{5 x+9}\\left(x^{2}+6 x+9\\right)+\\log _{x+3}\\left(5 x^{2}+24 x+27\\right)=4\n$$", "answer": "0,-1,-\\frac{3}{2}"}, {"index": 261, "question": "For each positive integer $N$, an Eden sequence from $\\{1,2,3, \\ldots, N\\}$ is defined to be a sequence that satisfies the following conditions:\n\n(i) each of its terms is an element of the set of consecutive integers $\\{1,2,3, \\ldots, N\\}$,\n\n(ii) the sequence is increasing, and\n\n(iii) the terms in odd numbered positions are odd and the terms in even numbered positions are even.\n\nFor example, the four Eden sequences from $\\{1,2,3\\}$ are\n\n$$\n\\begin{array}{llll}\n1 & 3 & 1,2 & 1,2,3\n\\end{array}\n$$\nDetermine the number of Eden sequences from $\\{1,2,3,4,5\\}$.", "answer": "12"}, {"index": 262, "question": "For each positive integer $N$, an Eden sequence from $\\{1,2,3, \\ldots, N\\}$ is defined to be a sequence that satisfies the following conditions:\n\n(i) each of its terms is an element of the set of consecutive integers $\\{1,2,3, \\ldots, N\\}$,\n\n(ii) the sequence is increasing, and\n\n(iii) the terms in odd numbered positions are odd and the terms in even numbered positions are even.\n\nFor example, the four Eden sequences from $\\{1,2,3\\}$ are\n\n$$\n\\begin{array}{llll}\n1 & 3 & 1,2 & 1,2,3\n\\end{array}\n$$\nFor each positive integer $N$, define $e(N)$ to be the number of Eden sequences from $\\{1,2,3, \\ldots, N\\}$. If $e(17)=4180$ and $e(20)=17710$, determine $e(18)$ and $e(19)$.", "answer": "6764,10945"}, {"index": 263, "question": "If $a$ is chosen randomly from the set $\\{1,2,3,4,5\\}$ and $b$ is chosen randomly from the set $\\{6,7,8\\}$, what is the probability that $a^{b}$ is an even number?", "answer": "\\frac{2}{5}"}, {"index": 264, "question": "A bag contains some blue and some green hats. On each turn, <PERSON> removes one hat without looking, with each hat in the bag being equally likely to be chosen. If it is green, she adds a blue hat into the bag from her supply of extra hats, and if it is blue, she adds a green hat to the bag. The bag initially contains 4 blue hats and 2 green hats. What is the probability that the bag again contains 4 blue hats and 2 green hats after two turns?", "answer": "\\frac{11}{18}"}, {"index": 265, "question": "Suppose that, for some angles $x$ and $y$,\n\n$$\n\\begin{aligned}\n& \\sin ^{2} x+\\cos ^{2} y=\\frac{3}{2} a \\\\\n& \\cos ^{2} x+\\sin ^{2} y=\\frac{1}{2} a^{2}\n\\end{aligned}\n$$\n\nDetermine the possible value(s) of $a$.", "answer": "1"}, {"index": 266, "question": "The sequence $2,5,10,50,500, \\ldots$ is formed so that each term after the second is the product of the two previous terms. The 15 th term ends with exactly $k$ zeroes. What is the value of $k$ ?", "answer": "233"}, {"index": 267, "question": "If $\\log _{2} x-2 \\log _{2} y=2$, determine $y$, as a function of $x$", "answer": "\\frac{1}{2},\\sqrt{x}"}, {"index": 268, "question": "Define $f(x)=\\sin ^{6} x+\\cos ^{6} x+k\\left(\\sin ^{4} x+\\cos ^{4} x\\right)$ for some real number $k$.\nDetermine all real numbers $k$ for which $f(x)$ is constant for all values of $x$.", "answer": "-\\frac{3}{2}"}, {"index": 269, "question": "Define $f(x)=\\sin ^{6} x+\\cos ^{6} x+k\\left(\\sin ^{4} x+\\cos ^{4} x\\right)$ for some real number $k$.\nIf $k=-0.7$, determine all solutions to the equation $f(x)=0$.", "answer": "x=\\frac{1}{6}\\pi+\\pik,\\frac{1}{3}\\pi+\\pik,\\frac{2}{3}\\pi+\\pik,\\frac{5}{6}\\pi+\\pik"}, {"index": 270, "question": "Define $f(x)=\\sin ^{6} x+\\cos ^{6} x+k\\left(\\sin ^{4} x+\\cos ^{4} x\\right)$ for some real number $k$.\nDetermine all real numbers $k$ for which there exists a real number $c$ such that $f(c)=0$.", "answer": "[-1,-\\frac{1}{2}]"}, {"index": 271, "question": "Hexagon $A B C D E F$ has vertices $A(0,0), B(4,0), C(7,2), D(7,5), E(3,5)$, $F(0,3)$. What is the area of hexagon $A B C D E F$ ?", "answer": "29"}, {"index": 272, "question": "A list $a_{1}, a_{2}, a_{3}, a_{4}$ of rational numbers is defined so that if one term is equal to $r$, then the next term is equal to $1+\\frac{1}{1+r}$. For example, if $a_{3}=\\frac{41}{29}$, then $a_{4}=1+\\frac{1}{1+(41 / 29)}=\\frac{99}{70}$. If $a_{3}=\\frac{41}{29}$, what is the value of $a_{1} ?$", "answer": "\\frac{7}{5}"}, {"index": 273, "question": "A hollow cylindrical tube has a radius of $10 \\mathrm{~mm}$ and a height of $100 \\mathrm{~mm}$. The tube sits flat on one of its circular faces on a horizontal table. The tube is filled with water to a depth of $h \\mathrm{~mm}$. A solid cylindrical rod has a radius of $2.5 \\mathrm{~mm}$ and a height of $150 \\mathrm{~mm}$. The rod is inserted into the tube so that one of its circular faces sits flat on the bottom of the tube. The height of the water in the tube is now $64 \\mathrm{~mm}$. Determine the value of $h$.", "answer": "60"}, {"index": 274, "question": "A function $f$ has the property that $f\\left(\\frac{2 x+1}{x}\\right)=x+6$ for all real values of $x \\neq 0$. What is the value of $f(4) ?$", "answer": "\\frac{13}{2}"}, {"index": 275, "question": "Determine all real numbers $a, b$ and $c$ for which the graph of the function $y=\\log _{a}(x+b)+c$ passes through the points $P(3,5), Q(5,4)$ and $R(11,3)$.", "answer": "\\frac{1}{3},-2,5"}, {"index": 276, "question": "A computer is programmed to choose an integer between 1 and 99, inclusive, so that the probability that it selects the integer $x$ is equal to $\\log _{100}\\left(1+\\frac{1}{x}\\right)$. Suppose that the probability that $81 \\leq x \\leq 99$ is equal to 2 times the probability that $x=n$ for some integer $n$. What is the value of $n$ ?", "answer": "9"}, {"index": 277, "question": "What is the smallest positive integer $x$ for which $\\frac{1}{32}=\\frac{x}{10^{y}}$ for some positive integer $y$ ?", "answer": "3125"}, {"index": 278, "question": "Determine all possible values for the area of a right-angled triangle with one side length equal to 60 and with the property that its side lengths form an arithmetic sequence.\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, $3,5,7,9$ are the first four terms of an arithmetic sequence.)", "answer": "2400,1350,864"}, {"index": 279, "question": "<PERSON><PERSON> and <PERSON> cross a lake in a straight line with the help of a one-seat kayak. Each can paddle the kayak at $7 \\mathrm{~km} / \\mathrm{h}$ and swim at $2 \\mathrm{~km} / \\mathrm{h}$. They start from the same point at the same time with <PERSON><PERSON> paddling and <PERSON> swimming. After a while, <PERSON><PERSON> stops the kayak and immediately starts swimming. Upon reaching the kayak (which has not moved since <PERSON><PERSON> started swimming), <PERSON> gets in and immediately starts paddling. They arrive on the far side of the lake at the same time, 90 minutes after they began. Determine the amount of time during these 90 minutes that the kayak was not being paddled.", "answer": "50"}, {"index": 280, "question": "Determine all pairs $(x, y)$ of real numbers that satisfy the system of equations\n\n$$\n\\begin{aligned}\nx\\left(\\frac{1}{2}+y-2 x^{2}\\right) & =0 \\\\\ny\\left(\\frac{5}{2}+x-y\\right) & =0\n\\end{aligned}\n$$", "answer": "(0,0),(0,\\frac{5}{2}),(\\frac{1}{2},0),(-\\frac{1}{2},0),(\\frac{3}{2},4),(-1,\\frac{3}{2})"}, {"index": 281, "question": "Determine all real numbers $x>0$ for which\n\n$$\n\\log _{4} x-\\log _{x} 16=\\frac{7}{6}-\\log _{x} 8\n$$", "answer": "2^{-2/3},8"}, {"index": 282, "question": "The string $A A A B B B A A B B$ is a string of ten letters, each of which is $A$ or $B$, that does not include the consecutive letters $A B B A$.\n\nThe string $A A A B B A A A B B$ is a string of ten letters, each of which is $A$ or $B$, that does include the consecutive letters $A B B A$.\n\nDetermine, with justification, the total number of strings of ten letters, each of which is $A$ or $B$, that do not include the consecutive letters $A B B A$.", "answer": "631"}, {"index": 283, "question": "Let $k$ be a positive integer with $k \\geq 2$. Two bags each contain $k$ balls, labelled with the positive integers from 1 to $k$. <PERSON> removes one ball from each bag. (In each bag, each ball is equally likely to be chosen.) Define $P(k)$ to be the probability that the product of the numbers on the two balls that he chooses is divisible by $k$.\nCalculate $P(10)$.", "answer": "\\frac{27}{100}"}, {"index": 284, "question": "In an arithmetic sequence, the first term is 1 and the last term is 19 . The sum of all the terms in the sequence is 70 . How many terms does the sequence have? (An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, 3, 5, 7, 9 is an arithmetic sequence with four terms.)", "answer": "7"}, {"index": 285, "question": "Suppose that $a(x+b(x+3))=2(x+6)$ for all values of $x$. Determine $a$ and $b$.", "answer": "-2,-2"}, {"index": 286, "question": "An integer $n$, with $100 \\leq n \\leq 999$, is chosen at random. What is the probability that the sum of the digits of $n$ is 24 ?", "answer": "\\frac{1}{90}"}, {"index": 287, "question": "The parabola $y=x^{2}-2 x+4$ is translated $p$ units to the right and $q$ units down. The $x$-intercepts of the resulting parabola are 3 and 5 . What are the values of $p$ and $q$ ?", "answer": "3,4"}, {"index": 288, "question": "If $\\log _{2} x,\\left(1+\\log _{4} x\\right)$, and $\\log _{8} 4 x$ are consecutive terms of a geometric sequence, determine the possible values of $x$.\n\n(A geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a constant. For example, $3,6,12$ is a geometric sequence with three terms.)", "answer": "64,\\frac{1}{4}"}, {"index": 289, "question": "Determine the two pairs of positive integers $(a, b)$ with $a<b$ that satisfy the equation $\\sqrt{a}+\\sqrt{b}=\\sqrt{50}$.", "answer": "(2,32),(8,18)"}, {"index": 290, "question": "Consider the system of equations:\n\n$$\n\\begin{aligned}\nc+d & =2000 \\\\\n\\frac{c}{d} & =k\n\\end{aligned}\n$$\n\nDetermine the number of integers $k$ with $k \\geq 0$ for which there is at least one pair of integers $(c, d)$ that is a solution to the system.", "answer": "20"}, {"index": 291, "question": "Determine all real numbers $x$ for which $2 \\log _{2}(x-1)=1-\\log _{2}(x+2)$.", "answer": "\\sqrt{3}"}, {"index": 292, "question": "Consider the function $f(x)=x^{2}-2 x$. Determine all real numbers $x$ that satisfy the equation $f(f(f(x)))=3$.", "answer": "3,1,-1,1+\\sqrt{2},1-\\sqrt{2}"}, {"index": 293, "question": "Suppose that $x$ satisfies $0<x<\\frac{\\pi}{2}$ and $\\cos \\left(\\frac{3}{2} \\cos x\\right)=\\sin \\left(\\frac{3}{2} \\sin x\\right)$.\n\nDetermine all possible values of $\\sin 2 x$, expressing your answers in the form $\\frac{a \\pi^{2}+b \\pi+c}{d}$ where $a, b, c, d$ are integers.", "answer": "\\frac{\\pi^{2}-9}{9}"}, {"index": 294, "question": "For positive integers $a$ and $b$, define $f(a, b)=\\frac{a}{b}+\\frac{b}{a}+\\frac{1}{a b}$.\n\nFor example, the value of $f(1,2)$ is 3 .\nDetermine the value of $f(2,5)$.", "answer": "3"}, {"index": 295, "question": "For positive integers $a$ and $b$, define $f(a, b)=\\frac{a}{b}+\\frac{b}{a}+\\frac{1}{a b}$.\n\nFor example, the value of $f(1,2)$ is 3 .\nDetermine all positive integers $a$ for which $f(a, a)$ is an integer.", "answer": "1"}, {"index": 296, "question": "<PERSON> and <PERSON><PERSON><PERSON> play a card game. <PERSON> starts with a hand of 6 cards: 2 red, 2 yellow and 2 green. <PERSON><PERSON><PERSON> starts with a hand of 4 cards: 2 purple and 2 white. <PERSON> plays first. <PERSON> and <PERSON><PERSON><PERSON> alternate turns. On each turn, the current player chooses one of their own cards at random and places it on the table. The cards remain on the table for the rest of the game. A player wins and the game ends when they have placed two cards of the same colour on the table. Determine the probability that <PERSON> wins the game.", "answer": "\\frac{7}{15}"}, {"index": 297, "question": "Consider the sequence $t_{1}=1, t_{2}=-1$ and $t_{n}=\\left(\\frac{n-3}{n-1}\\right) t_{n-2}$ where $n \\geq 3$. What is the value of $t_{1998}$ ?", "answer": "\\frac{-1}{1997}"}, {"index": 298, "question": "The $n$th term of an arithmetic sequence is given by $t_{n}=555-7 n$.\n\nIf $S_{n}=t_{1}+t_{2}+\\ldots+t_{n}$, determine the smallest value of $n$ for which $S_{n}<0$.", "answer": "158"}, {"index": 299, "question": "If $x$ and $y$ are real numbers, determine all solutions $(x, y)$ of the system of equations\n\n$$\n\\begin{aligned}\n& x^{2}-x y+8=0 \\\\\n& x^{2}-8 x+y=0\n\\end{aligned}\n$$", "answer": "(-1,-9),(4+2\\sqrt{2},8),(4-2\\sqrt{2},8)"}, {"index": 300, "question": "The equations $x^{2}+5 x+6=0$ and $x^{2}+5 x-6=0$ each have integer solutions whereas only one of the equations in the pair $x^{2}+4 x+5=0$ and $x^{2}+4 x-5=0$ has integer solutions.\nDetermine $q$ in terms of $a$ and $b$.", "answer": "\\frac{ab}{2}"}, {"index": 301, "question": "Determine all values of $k$, with $k \\neq 0$, for which the parabola\n\n$$\ny=k x^{2}+(5 k+3) x+(6 k+5)\n$$\n\nhas its vertex on the $x$-axis.", "answer": "-1,-9"}, {"index": 302, "question": "The function $f(x)$ satisfies the equation $f(x)=f(x-1)+f(x+1)$ for all values of $x$. If $f(1)=1$ and $f(2)=3$, what is the value of $f(2008)$ ?", "answer": "-1"}, {"index": 303, "question": "The numbers $a, b, c$, in that order, form a three term arithmetic sequence (see below) and $a+b+c=60$.\n\nThe numbers $a-2, b, c+3$, in that order, form a three term geometric sequence. Determine all possible values of $a, b$ and $c$.\n\n(An arithmetic sequence is a sequence in which each term after the first is obtained from the previous term by adding a constant. For example, $3,5,7$ is an arithmetic sequence with three terms.\n\nA geometric sequence is a sequence in which each term after the first is obtained from the previous term by multiplying it by a constant. For example, $3,6,12$ is a geometric sequence with three terms.)\n\nPresent your answer in the form of coordinates (e.g. (1, 2, 3) for a=1, b=2, c=3).", "answer": "(27,20,13),(18,20,22)"}, {"index": 304, "question": "The average of three consecutive multiples of 3 is $a$.\n\nThe average of four consecutive multiples of 4 is $a+27$.\n\nThe average of the smallest and largest of these seven integers is 42 .\n\nDetermine the value of $a$.", "answer": "27"}, {"index": 305, "question": "<PERSON> and <PERSON> each have a bag of 9 balls. The balls in each bag are numbered from 1 to 9. <PERSON> and <PERSON> each remove one ball from their own bag. Let $b$ be the sum of the numbers on the balls remaining in <PERSON>'s bag. Let $c$ be the sum of the numbers on the balls remaining in <PERSON>'s bag. Determine the probability that $b$ and $c$ differ by a multiple of 4 .", "answer": "\\frac{7}{27}"}, {"index": 306, "question": "The equation $2^{x+2} 5^{6-x}=10^{x^{2}}$ has two real solutions. Determine these two solutions.", "answer": "2,-\\log_{10}250"}, {"index": 307, "question": "Determine all real solutions to the system of equations\n\n$$\n\\begin{aligned}\n& x+\\log _{10} x=y-1 \\\\\n& y+\\log _{10}(y-1)=z-1 \\\\\n& z+\\log _{10}(z-2)=x+2\n\\end{aligned}\n$$\n\nand prove that there are no more solutions.", "answer": "1,2,3"}, {"index": 308, "question": "The positive integers 34 and 80 have exactly two positive common divisors, namely 1 and 2. How many positive integers $n$ with $1 \\leq n \\leq 30$ have the property that $n$ and 80 have exactly two positive common divisors?", "answer": "9"}, {"index": 309, "question": "A function $f$ is defined so that\n\n- $f(1)=1$,\n- if $n$ is an even positive integer, then $f(n)=f\\left(\\frac{1}{2} n\\right)$, and\n- if $n$ is an odd positive integer with $n>1$, then $f(n)=f(n-1)+1$.\n\nFor example, $f(34)=f(17)$ and $f(17)=f(16)+1$.\n\nDetermine the value of $f(50)$.", "answer": "3"}, {"index": 310, "question": "The perimeter of equilateral $\\triangle P Q R$ is 12. The perimeter of regular hexagon $S T U V W X$ is also 12. What is the ratio of the area of $\\triangle P Q R$ to the area of $S T U V W X$ ?", "answer": "\\frac{2}{3}"}, {"index": 311, "question": "For how many integers $k$ with $0<k<18$ is $\\frac{5 \\sin \\left(10 k^{\\circ}\\right)-2}{\\sin ^{2}\\left(10 k^{\\circ}\\right)} \\geq 2$ ?", "answer": "13"}, {"index": 312, "question": "Eight people, including triplets <PERSON>, <PERSON> and <PERSON>, are going for a trip in four canoes. Each canoe seats two people. The eight people are to be randomly assigned to the four canoes in pairs. What is the probability that no two of <PERSON>, <PERSON> and <PERSON> will be in the same canoe?", "answer": "\\frac{4}{7}"}, {"index": 313, "question": "Diagonal $W Y$ of square $W X Y Z$ has slope 2. Determine the sum of the slopes of $W X$ and $X Y$.", "answer": "-\\frac{8}{3}"}, {"index": 314, "question": "Determine all values of $x$ such that $\\log _{2 x}(48 \\sqrt[3]{3})=\\log _{3 x}(162 \\sqrt[3]{2})$.", "answer": "\\sqrt{6}"}, {"index": 315, "question": "In an infinite array with two rows, the numbers in the top row are denoted $\\ldots, A_{-2}, A_{-1}, A_{0}, A_{1}, A_{2}, \\ldots$ and the numbers in the bottom row are denoted $\\ldots, B_{-2}, B_{-1}, B_{0}, B_{1}, B_{2}, \\ldots$ For each integer $k$, the entry $A_{k}$ is directly above the entry $B_{k}$ in the array, as shown:\n\n| $\\ldots$ | $A_{-2}$ | $A_{-1}$ | $A_{0}$ | $A_{1}$ | $A_{2}$ | $\\ldots$ |\n| :--- | :--- | :--- | :--- | :--- | :--- | :--- |\n| $\\ldots$ | $B_{-2}$ | $B_{-1}$ | $B_{0}$ | $B_{1}$ | $B_{2}$ | $\\ldots$ |\n\nFor each integer $k, A_{k}$ is the average of the entry to its left, the entry to its right, and the entry below it; similarly, each entry $B_{k}$ is the average of the entry to its left, the entry to its right, and the entry above it.\nIn one such array, $A_{0}=A_{1}=A_{2}=0$ and $A_{3}=1$.\n\nDetermine the value of $A_{4}$.", "answer": "6"}, {"index": 316, "question": "The populations of Alphaville and Betaville were equal at the end of 1995. The population of Alphaville decreased by $2.9 \\%$ during 1996, then increased by $8.9 \\%$ during 1997 , and then increased by $6.9 \\%$ during 1998 . The population of Betaville increased by $r \\%$ in each of the three years. If the populations of the towns are equal at the end of 1998, determine the value of $r$ correct to one decimal place.", "answer": "4.2"}, {"index": 317, "question": "Determine the coordinates of the points of intersection of the graphs of $y=\\log _{10}(x-2)$ and $y=1-\\log _{10}(x+1)$.", "answer": "(4,\\log_{10}2)"}, {"index": 318, "question": "<PERSON> was born in the twentieth century. On his birthday in the present year (2014), he notices that his current age is twice the number formed by the rightmost two digits of the year in which he was born. Compute the four-digit year in which <PERSON> was born.", "answer": "1938"}, {"index": 319, "question": "Let $A, B$, and $C$ be randomly chosen (not necessarily distinct) integers between 0 and 4 inclusive. <PERSON> and <PERSON> compute the value of $A+B \\cdot C$ by two different methods. <PERSON> follows the proper order of operations, computing $A+(B \\cdot C)$. <PERSON> ignores order of operations, choosing instead to compute $(A+B) \\cdot C$. Compute the probability that <PERSON> and <PERSON> get the same answer.", "answer": "\\frac{9}{25}"}, {"index": 320, "question": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> line up for ice cream. In an acceptable lineup, <PERSON> is ahead of <PERSON>, <PERSON> is ahead of <PERSON>, <PERSON> is ahead of <PERSON>, and <PERSON> is ahead of <PERSON>. For example, the lineup with <PERSON> in front, followed by <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, in that order, is an acceptable lineup. Compute the number of acceptable lineups.", "answer": "20"}, {"index": 321, "question": "In triangle $A B C, a=12, b=17$, and $c=13$. Compute $b \\cos C-c \\cos B$.", "answer": "10"}, {"index": 322, "question": "The sequence of words $\\left\\{a_{n}\\right\\}$ is defined as follows: $a_{1}=X, a_{2}=O$, and for $n \\geq 3, a_{n}$ is $a_{n-1}$ followed by the reverse of $a_{n-2}$. For example, $a_{3}=O X, a_{4}=O X O, a_{5}=O X O X O$, and $a_{6}=O X O X O O X O$. Compute the number of palindromes in the first 1000 terms of this sequence.", "answer": "667"}, {"index": 323, "question": "Compute the smallest positive integer $n$ such that $214 \\cdot n$ and $2014 \\cdot n$ have the same number of divisors.", "answer": "19133"}, {"index": 324, "question": "Let $N$ be the least integer greater than 20 that is a palindrome in both base 20 and base 14 . For example, the three-digit base-14 numeral (13)5(13) ${ }_{14}$ (representing $13 \\cdot 14^{2}+5 \\cdot 14^{1}+13 \\cdot 14^{0}$ ) is a palindrome in base 14 , but not in base 20 , and the three-digit base-14 numeral (13)31 14 is not a palindrome in base 14 . Compute the base-10 representation of $N$.", "answer": "105"}, {"index": 325, "question": "$\\quad$ Compute the greatest integer $k \\leq 1000$ such that $\\left(\\begin{array}{c}1000 \\\\ k\\end{array}\\right)$ is a multiple of 7 .", "answer": "979"}, {"index": 326, "question": "An integer-valued function $f$ is called tenuous if $f(x)+f(y)>x^{2}$ for all positive integers $x$ and $y$. Let $g$ be a tenuous function such that $g(1)+g(2)+\\cdots+g(20)$ is as small as possible. Compute the minimum possible value for $g(14)$.", "answer": "136"}, {"index": 327, "question": "Let $T=(0,0), N=(2,0), Y=(6,6), W=(2,6)$, and $R=(0,2)$. Compute the area of pentagon $T N Y W R$.", "answer": "20"}, {"index": 328, "question": "Let $T=20$. The lengths of the sides of a rectangle are the zeroes of the polynomial $x^{2}-3 T x+T^{2}$. Compute the length of the rectangle's diagonal.", "answer": "20\\sqrt{7}"}, {"index": 329, "question": "Let $T=20 \\sqrt{7}$. Let $w>0$ be a real number such that $T$ is the area of the region above the $x$-axis, below the graph of $y=\\lceil x\\rceil^{2}$, and between the lines $x=0$ and $x=w$. Compute $\\lceil 2 w\\rceil$.", "answer": "10"}, {"index": 330, "question": "Compute the least positive integer $n$ such that $\\operatorname{gcd}\\left(n^{3}, n !\\right) \\geq 100$.", "answer": "8"}, {"index": 331, "question": "Let $T=8$. At a party, everyone shakes hands with everyone else exactly once, except <PERSON>, who leaves early. A grand total of $20 T$ handshakes take place. Compute the number of people at the party who shook hands with <PERSON>.", "answer": "7"}, {"index": 332, "question": "Let $T=7$. Given the sequence $u_{n}$ such that $u_{3}=5, u_{6}=89$, and $u_{n+2}=3 u_{n+1}-u_{n}$ for integers $n \\geq 1$, compute $u_{T}$.", "answer": "233"}, {"index": 333, "question": "In each town in ARMLandia, the residents have formed groups, which meet each week to share math problems and enjoy each others' company over a potluck-style dinner. Each town resident belongs to exactly one group. Every week, each resident is required to make one dish and to bring it to his/her group.\n\nIt so happens that each resident knows how to make precisely two dishes. Moreover, no two residents of a town know how to make the same pair of dishes. Shown below are two example towns. In the left column are the names of the town's residents. Adjacent to each name is the list of dishes that the corresponding resident knows how to make.\n\n| ARMLton |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Paul | pie, turkey |\n| Arnold | pie, salad |\n| Kelly | salad, broth |\n\n\n| ARMLville |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Sally | steak, calzones |\n| Ross | calzones, pancakes |\n| David | steak, pancakes |\n\nThe population of a town $T$, denoted $\\operatorname{pop}(T)$, is the number of residents of $T$. Formally, the town itself is simply the set of its residents, denoted by $\\left\\{r_{1}, \\ldots, r_{\\mathrm{pop}(T)}\\right\\}$ unless otherwise specified. The set of dishes that the residents of $T$ collectively know how to make is denoted $\\operatorname{dish}(T)$. For example, in the town of ARMLton described above, pop(ARMLton) $=3$, and dish(ARMLton) $=$ \\{pie, turkey, salad, broth\\}.\n\nA town $T$ is called full if for every pair of dishes in $\\operatorname{dish}(T)$, there is exactly one resident in $T$ who knows how to make those two dishes. In the examples above, ARMLville is a full town, but ARMLton is not, because (for example) nobody in ARMLton knows how to make both turkey and salad.\n\nDenote by $\\mathcal{F}_{d}$ a full town in which collectively the residents know how to make $d$ dishes. That is, $\\left|\\operatorname{dish}\\left(\\mathcal{F}_{d}\\right)\\right|=d$.\nCompute $\\operatorname{pop}\\left(\\mathcal{F}_{17}\\right)$.", "answer": "136"}, {"index": 334, "question": "In each town in ARMLandia, the residents have formed groups, which meet each week to share math problems and enjoy each others' company over a potluck-style dinner. Each town resident belongs to exactly one group. Every week, each resident is required to make one dish and to bring it to his/her group.\n\nIt so happens that each resident knows how to make precisely two dishes. Moreover, no two residents of a town know how to make the same pair of dishes. Shown below are two example towns. In the left column are the names of the town's residents. Adjacent to each name is the list of dishes that the corresponding resident knows how to make.\n\n| ARMLton |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Paul | pie, turkey |\n| Arnold | pie, salad |\n| Kelly | salad, broth |\n\n\n| ARMLville |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Sally | steak, calzones |\n| Ross | calzones, pancakes |\n| David | steak, pancakes |\n\nThe population of a town $T$, denoted $\\operatorname{pop}(T)$, is the number of residents of $T$. Formally, the town itself is simply the set of its residents, denoted by $\\left\\{r_{1}, \\ldots, r_{\\mathrm{pop}(T)}\\right\\}$ unless otherwise specified. The set of dishes that the residents of $T$ collectively know how to make is denoted $\\operatorname{dish}(T)$. For example, in the town of ARMLton described above, pop(ARMLton) $=3$, and dish(ARMLton) $=$ \\{pie, turkey, salad, broth\\}.\n\nA town $T$ is called full if for every pair of dishes in $\\operatorname{dish}(T)$, there is exactly one resident in $T$ who knows how to make those two dishes. In the examples above, ARMLville is a full town, but ARMLton is not, because (for example) nobody in ARMLton knows how to make both turkey and salad.\n\nDenote by $\\mathcal{F}_{d}$ a full town in which collectively the residents know how to make $d$ dishes. That is, $\\left|\\operatorname{dish}\\left(\\mathcal{F}_{d}\\right)\\right|=d$.\nLet $n=\\operatorname{pop}\\left(\\mathcal{F}_{d}\\right)$. In terms of $n$, compute $d$.", "answer": "d=\\frac{1+\\sqrt{1+8n}}{2}"}, {"index": 335, "question": "In each town in ARMLandia, the residents have formed groups, which meet each week to share math problems and enjoy each others' company over a potluck-style dinner. Each town resident belongs to exactly one group. Every week, each resident is required to make one dish and to bring it to his/her group.\n\nIt so happens that each resident knows how to make precisely two dishes. Moreover, no two residents of a town know how to make the same pair of dishes. Shown below are two example towns. In the left column are the names of the town's residents. Adjacent to each name is the list of dishes that the corresponding resident knows how to make.\n\n| ARMLton |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Paul | pie, turkey |\n| Arnold | pie, salad |\n| Kelly | salad, broth |\n\n\n| ARMLville |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Sally | steak, calzones |\n| Ross | calzones, pancakes |\n| David | steak, pancakes |\n\nThe population of a town $T$, denoted $\\operatorname{pop}(T)$, is the number of residents of $T$. Formally, the town itself is simply the set of its residents, denoted by $\\left\\{r_{1}, \\ldots, r_{\\mathrm{pop}(T)}\\right\\}$ unless otherwise specified. The set of dishes that the residents of $T$ collectively know how to make is denoted $\\operatorname{dish}(T)$. For example, in the town of ARMLton described above, pop(ARMLton) $=3$, and dish(ARMLton) $=$ \\{pie, turkey, salad, broth\\}.\n\nA town $T$ is called full if for every pair of dishes in $\\operatorname{dish}(T)$, there is exactly one resident in $T$ who knows how to make those two dishes. In the examples above, ARMLville is a full town, but ARMLton is not, because (for example) nobody in ARMLton knows how to make both turkey and salad.\n\nDenote by $\\mathcal{F}_{d}$ a full town in which collectively the residents know how to make $d$ dishes. That is, $\\left|\\operatorname{dish}\\left(\\mathcal{F}_{d}\\right)\\right|=d$.\n\nIn order to avoid the embarrassing situation where two people bring the same dish to a group dinner, if two people know how to make a common dish, they are forbidden from participating in the same group meeting. Formally, a group assignment on $T$ is a function $f: T \\rightarrow\\{1,2, \\ldots, k\\}$, satisfying the condition that if $f\\left(r_{i}\\right)=f\\left(r_{j}\\right)$ for $i \\neq j$, then $r_{i}$ and $r_{j}$ do not know any of the same recipes. The group number of a town $T$, denoted $\\operatorname{gr}(T)$, is the least positive integer $k$ for which there exists a group assignment on $T$.\n\nFor example, consider once again the town of ARMLton. A valid group assignment would be $f($ Paul $)=f($ Kelly $)=1$ and $f($ Arnold $)=2$. The function which gives the value 1 to each resident of ARMLton is not a group assignment, because Paul and Arnold must be assigned to different groups.\n\n\nFor a dish $D$, a resident is called a $D$-chef if he or she knows how to make the dish $D$. Define $\\operatorname{chef}_{T}(D)$ to be the set of residents in $T$ who are $D$-chefs. For example, in ARMLville, David is a steak-chef and a pancakes-chef. Further, $\\operatorname{chef}_{\\text {ARMLville }}($ steak $)=\\{$ Sally, David $\\}$.\n\n\nIf $\\operatorname{gr}(T)=\\left|\\operatorname{chef}_{T}(D)\\right|$ for some $D \\in \\operatorname{dish}(T)$, then $T$ is called homogeneous. If $\\operatorname{gr}(T)>\\left|\\operatorname{chef}_{T}(D)\\right|$ for each dish $D \\in \\operatorname{dish}(T)$, then $T$ is called heterogeneous. For example, ARMLton is homogeneous, because $\\operatorname{gr}($ ARMLton $)=2$ and exactly two chefs make pie, but ARMLville is heterogeneous, because even though each dish is only cooked by two chefs, $\\operatorname{gr}($ ARMLville $)=3$.\n\n\nA resident cycle is a sequence of distinct residents $r_{1}, \\ldots, r_{n}$ such that for each $1 \\leq i \\leq n-1$, the residents $r_{i}$ and $r_{i+1}$ know how to make a common dish, residents $r_{n}$ and $r_{1}$ know how to make a common dish, and no other pair of residents $r_{i}$ and $r_{j}, 1 \\leq i, j \\leq n$ know how to make a common dish. Two resident cycles are indistinguishable if they contain the same residents (in any order), and distinguishable otherwise. For example, if $r_{1}, r_{2}, r_{3}, r_{4}$ is a resident cycle, then $r_{2}, r_{1}, r_{4}, r_{3}$ and $r_{3}, r_{2}, r_{1}, r_{4}$ are indistinguishable resident cycles.\nCompute the number of distinguishable resident cycles of length 6 in $\\mathcal{F}_{8}$.", "answer": "1680"}, {"index": 336, "question": "In each town in ARMLandia, the residents have formed groups, which meet each week to share math problems and enjoy each others' company over a potluck-style dinner. Each town resident belongs to exactly one group. Every week, each resident is required to make one dish and to bring it to his/her group.\n\nIt so happens that each resident knows how to make precisely two dishes. Moreover, no two residents of a town know how to make the same pair of dishes. Shown below are two example towns. In the left column are the names of the town's residents. Adjacent to each name is the list of dishes that the corresponding resident knows how to make.\n\n| ARMLton |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Paul | pie, turkey |\n| Arnold | pie, salad |\n| Kelly | salad, broth |\n\n\n| ARMLville |  |\n| :--- | :--- |\n| Resident | Dishes |\n| Sally | steak, calzones |\n| Ross | calzones, pancakes |\n| David | steak, pancakes |\n\nThe population of a town $T$, denoted $\\operatorname{pop}(T)$, is the number of residents of $T$. Formally, the town itself is simply the set of its residents, denoted by $\\left\\{r_{1}, \\ldots, r_{\\mathrm{pop}(T)}\\right\\}$ unless otherwise specified. The set of dishes that the residents of $T$ collectively know how to make is denoted $\\operatorname{dish}(T)$. For example, in the town of ARMLton described above, pop(ARMLton) $=3$, and dish(ARMLton) $=$ \\{pie, turkey, salad, broth\\}.\n\nA town $T$ is called full if for every pair of dishes in $\\operatorname{dish}(T)$, there is exactly one resident in $T$ who knows how to make those two dishes. In the examples above, ARMLville is a full town, but ARMLton is not, because (for example) nobody in ARMLton knows how to make both turkey and salad.\n\nDenote by $\\mathcal{F}_{d}$ a full town in which collectively the residents know how to make $d$ dishes. That is, $\\left|\\operatorname{dish}\\left(\\mathcal{F}_{d}\\right)\\right|=d$.\n\nIn order to avoid the embarrassing situation where two people bring the same dish to a group dinner, if two people know how to make a common dish, they are forbidden from participating in the same group meeting. Formally, a group assignment on $T$ is a function $f: T \\rightarrow\\{1,2, \\ldots, k\\}$, satisfying the condition that if $f\\left(r_{i}\\right)=f\\left(r_{j}\\right)$ for $i \\neq j$, then $r_{i}$ and $r_{j}$ do not know any of the same recipes. The group number of a town $T$, denoted $\\operatorname{gr}(T)$, is the least positive integer $k$ for which there exists a group assignment on $T$.\n\nFor example, consider once again the town of ARMLton. A valid group assignment would be $f($ Paul $)=f($ Kelly $)=1$ and $f($ Arnold $)=2$. The function which gives the value 1 to each resident of ARMLton is not a group assignment, because Paul and Arnold must be assigned to different groups.\n\n\nFor a dish $D$, a resident is called a $D$-chef if he or she knows how to make the dish $D$. Define $\\operatorname{chef}_{T}(D)$ to be the set of residents in $T$ who are $D$-chefs. For example, in ARMLville, David is a steak-chef and a pancakes-chef. Further, $\\operatorname{chef}_{\\text {ARMLville }}($ steak $)=\\{$ Sally, David $\\}$.\n\n\nIf $\\operatorname{gr}(T)=\\left|\\operatorname{chef}_{T}(D)\\right|$ for some $D \\in \\operatorname{dish}(T)$, then $T$ is called homogeneous. If $\\operatorname{gr}(T)>\\left|\\operatorname{chef}_{T}(D)\\right|$ for each dish $D \\in \\operatorname{dish}(T)$, then $T$ is called heterogeneous. For example, ARMLton is homogeneous, because $\\operatorname{gr}($ ARMLton $)=2$ and exactly two chefs make pie, but ARMLville is heterogeneous, because even though each dish is only cooked by two chefs, $\\operatorname{gr}($ ARMLville $)=3$.\n\n\nA resident cycle is a sequence of distinct residents $r_{1}, \\ldots, r_{n}$ such that for each $1 \\leq i \\leq n-1$, the residents $r_{i}$ and $r_{i+1}$ know how to make a common dish, residents $r_{n}$ and $r_{1}$ know how to make a common dish, and no other pair of residents $r_{i}$ and $r_{j}, 1 \\leq i, j \\leq n$ know how to make a common dish. Two resident cycles are indistinguishable if they contain the same residents (in any order), and distinguishable otherwise. For example, if $r_{1}, r_{2}, r_{3}, r_{4}$ is a resident cycle, then $r_{2}, r_{1}, r_{4}, r_{3}$ and $r_{3}, r_{2}, r_{1}, r_{4}$ are indistinguishable resident cycles.\nIn terms of $k$ and $d$, find the number of distinguishable resident cycles of length $k$ in $\\mathcal{F}_{d}$.", "answer": "\\frac{d!}{2k(d-k)!}"}, {"index": 337, "question": "A student computed the repeating decimal expansion of $\\frac{1}{N}$ for some integer $N$, but inserted six extra digits into the repetend to get $.0 \\overline{0231846597}$. Compute the value of $N$.", "answer": "606"}, {"index": 338, "question": "Let $n$ be a four-digit number whose square root is three times the sum of the digits of $n$. Compute $n$.", "answer": "2916"}, {"index": 339, "question": "Compute the sum of the reciprocals of the positive integer divisors of 24.", "answer": "\\frac{5}{2}"}, {"index": 340, "question": "There exists a digit $Y$ such that, for any digit $X$, the seven-digit number $\\underline{1} \\underline{2} \\underline{3} \\underline{X} \\underline{5} \\underline{Y} \\underline{7}$ is not a multiple of 11. Compute $Y$.", "answer": "4"}, {"index": 341, "question": "A point is selected at random from the interior of a right triangle with legs of length $2 \\sqrt{3}$ and 4 . Let $p$ be the probability that the distance between the point and the nearest vertex is less than 2. Then $p$ can be written in the form $a+\\sqrt{b} \\pi$, where $a$ and $b$ are rational numbers. Compute $(a, b)$.", "answer": "(\\frac{1}{4},\\frac{1}{27})"}, {"index": 342, "question": "The square $A R M L$ is contained in the $x y$-plane with $A=(0,0)$ and $M=(1,1)$. Compute the length of the shortest path from the point $(2 / 7,3 / 7)$ to itself that touches three of the four sides of square $A R M L$.", "answer": "\\frac{2}{7}\\sqrt{53}"}, {"index": 343, "question": "For each positive integer $k$, let $S_{k}$ denote the infinite arithmetic sequence of integers with first term $k$ and common difference $k^{2}$. For example, $S_{3}$ is the sequence $3,12,21, \\ldots$ Compute the sum of all $k$ such that 306 is an element of $S_{k}$.", "answer": "326"}, {"index": 344, "question": "Compute the sum of all values of $k$ for which there exist positive real numbers $x$ and $y$ satisfying the following system of equations.\n\n$$\n\\left\\{\\begin{aligned}\n\\log _{x} y^{2}+\\log _{y} x^{5} & =2 k-1 \\\\\n\\log _{x^{2}} y^{5}-\\log _{y^{2}} x^{3} & =k-3\n\\end{aligned}\\right.\n$$", "answer": "\\frac{43}{48}"}, {"index": 345, "question": "Let $W=(0,0), A=(7,0), S=(7,1)$, and $H=(0,1)$. Compute the number of ways to tile rectangle $W A S H$ with triangles of area $1 / 2$ and vertices at lattice points on the boundary of WASH.", "answer": "3432"}, {"index": 346, "question": "Compute $\\sin ^{2} 4^{\\circ}+\\sin ^{2} 8^{\\circ}+\\sin ^{2} 12^{\\circ}+\\cdots+\\sin ^{2} 176^{\\circ}$.", "answer": "\\frac{45}{2}"}, {"index": 347, "question": "Compute the area of the region defined by $x^{2}+y^{2} \\leq|x|+|y|$.", "answer": "2+\\pi"}, {"index": 348, "question": "The arithmetic sequences $a_{1}, a_{2}, a_{3}, \\ldots, a_{20}$ and $b_{1}, b_{2}, b_{3}, \\ldots, b_{20}$ consist of 40 distinct positive integers, and $a_{20}+b_{14}=1000$. Compute the least possible value for $b_{20}+a_{14}$.", "answer": "10"}, {"index": 349, "question": "Compute the ordered triple $(x, y, z)$ representing the farthest lattice point from the origin that satisfies $x y-z^{2}=y^{2} z-x=14$.", "answer": "(-266,-3,-28)"}, {"index": 350, "question": "The sequence $a_{1}, a_{2}, a_{3}, \\ldots$ is a geometric sequence with $a_{20}=8$ and $a_{14}=2^{21}$. Compute $a_{21}$.", "answer": "1"}, {"index": 351, "question": "Let $T=1$. Circles $L$ and $O$ are internally tangent and have radii $T$ and $4 T$, respectively. Point $E$ lies on circle $L$ such that $\\overline{O E}$ is tangent to circle $L$. Compute $O E$.", "answer": "2\\sqrt{2}"}, {"index": 352, "question": "Let $T=2 \\sqrt{2}$. In a right triangle, one leg has length $T^{2}$ and the other leg is 2 less than the hypotenuse. Compute the triangle's perimeter.", "answer": "40"}, {"index": 353, "question": "$\\quad$ Let $T=40$. If $x+9 y=17$ and $T x+(T+1) y=T+2$, compute $20 x+14 y$.", "answer": "8"}, {"index": 354, "question": "Let $T=8$. Let $f(x)=a x^{2}+b x+c$. The product of the roots of $f$ is $T$. If $(-2,20)$ and $(1,14)$ lie on the graph of $f$, compute $a$.", "answer": "\\frac{8}{5}"}, {"index": 355, "question": "Let $T=\\frac{8}{5}$. Let $z_{1}=15+5 i$ and $z_{2}=1+K i$. Compute the smallest positive integral value of $K$ such that $\\left|z_{1}-z_{2}\\right| \\geq 15 T$.", "answer": "25"}, {"index": 356, "question": "Let $T=25$. Suppose that $T$ people are standing in a line, including three people named <PERSON>, <PERSON>, and <PERSON>. If the people are assigned their positions in line at random, compute the probability that <PERSON> is standing next to at least one of <PERSON> or <PERSON>.", "answer": "\\frac{47}{300}"}, {"index": 357, "question": "Let $A$ be the number you will receive from position 7 and let $B$ be the number you will receive from position 9. Let $\\alpha=\\sin ^{-1} A$ and let $\\beta=\\cos ^{-1} B$. Compute $\\sin (\\alpha+\\beta)+\\sin (\\alpha-\\beta)$.", "answer": "\\frac{94}{4225}"}, {"index": 358, "question": "Let $T=13$. If $r$ is the radius of a right circular cone and the cone's height is $T-r^{2}$, let $V$ be the maximum possible volume of the cone. Compute $\\pi / V$.", "answer": "\\frac{12}{169}"}, {"index": 359, "question": "Let $T=650$. If $\\log T=2-\\log 2+\\log k$, compute the value of $k$.", "answer": "13"}, {"index": 360, "question": "Let $T=100$. <PERSON><PERSON> has a flight from Rome to Athens that is scheduled to last for $T+30$ minutes. However, owing to a tailwind, her flight only lasts for $T$ minutes. The plane's speed is 1.5 miles per minute faster than what it would have been for the originally scheduled flight. Compute the distance (in miles) that the plane travels.", "answer": "650"}, {"index": 361, "question": "Let $T=9$. Compute $\\sqrt{\\sqrt{\\sqrt[T]{10^{T^{2}-T}}}}$.", "answer": "100"}, {"index": 362, "question": "Let $T=3$. Regular hexagon $S U P E R B$ has side length $\\sqrt{T}$. Compute the value of $B E \\cdot S U \\cdot R E$.", "answer": "9"}, {"index": 363, "question": "Let $T=70$. Chef <PERSON><PERSON> is preparing a burrito menu. A burrito consists of: (1) a choice of chicken, beef, turkey, or no meat, (2) exactly one of three types of beans, (3) exactly one of two types of rice, and (4) exactly one of $K$ types of cheese. Compute the smallest value of $K$ such that Chef <PERSON><PERSON> can make at least $T$ different burrito varieties.", "answer": "3"}, {"index": 364, "question": "Compute the smallest positive integer $N$ such that $20 N$ is a multiple of 14 and $14 N$ is a multiple of 20 .", "answer": "70"}, {"index": 365, "question": "Call a positive integer fibbish if each digit, after the leftmost two, is at least the sum of the previous two digits. Compute the greatest fibbish number.", "answer": "10112369"}, {"index": 366, "question": "An ARMLbar is a $7 \\times 7$ grid of unit squares with the center unit square removed. A portion of an ARMLbar is a square section of the bar, cut along the gridlines of the original bar. Compute the number of different ways there are to cut a single portion from an ARMLbar.", "answer": "96"}, {"index": 367, "question": "Regular hexagon $A B C D E F$ and regular hexagon $G H I J K L$ both have side length 24 . The hexagons overlap, so that $G$ is on $\\overline{A B}, B$ is on $\\overline{G H}, K$ is on $\\overline{D E}$, and $D$ is on $\\overline{J K}$. If $[G B C D K L]=\\frac{1}{2}[A B C D E F]$, compute $L F$.", "answer": "18"}, {"index": 368, "question": "Compute the largest base-10 integer $\\underline{A} \\underline{B} \\underline{C} \\underline{D}$, with $A>0$, such that $\\underline{A} \\underline{B} \\underline{C} \\underline{D}=B !+C !+D !$.", "answer": "5762"}, {"index": 369, "question": "Let $X$ be the number of digits in the decimal expansion of $100^{1000^{10,000}}$, and let $Y$ be the number of digits in the decimal expansion of $1000^{10,000^{100,000}}$. Compute $\\left\\lfloor\\log _{X} Y\\right\\rfloor$.", "answer": "13"}, {"index": 370, "question": "Compute the smallest possible value of $n$ such that two diagonals of a regular $n$-gon intersect at an angle of 159 degrees.", "answer": "60"}, {"index": 371, "question": "Compute the number of quadratic functions $f(x)=a x^{2}+b x+c$ with integer roots and integer coefficients whose graphs pass through the points $(0,0)$ and $(15,225)$.", "answer": "8"}, {"index": 372, "question": "A bubble in the shape of a hemisphere of radius 1 is on a tabletop. Inside the bubble are five congruent spherical marbles, four of which are sitting on the table and one which rests atop the others. All marbles are tangent to the bubble, and their centers can be connected to form a pyramid with volume $V$ and with a square base. Compute $V$.", "answer": "\\frac{1}{54}"}, {"index": 373, "question": "Compute the smallest positive integer base $b$ for which $16_{b}$ is prime and $97_{b}$ is a perfect square.", "answer": "53"}, {"index": 374, "question": "For a positive integer $n$, let $C(n)$ equal the number of pairs of consecutive 1's in the binary representation of $n$. For example, $C(183)=C\\left(10110111_{2}\\right)=3$. Compute $C(1)+C(2)+$ $C(3)+\\cdots+C(256)$.", "answer": "448"}, {"index": 375, "question": "A set $S$ contains thirteen distinct positive integers whose sum is 120 . Compute the largest possible value for the median of $S$.", "answer": "11"}, {"index": 376, "question": "Let $T=11$. Compute the least positive integer $b$ such that, when expressed in base $b$, the number $T$ ! ends in exactly two zeroes.", "answer": "5"}, {"index": 377, "question": "Let $T=5$. Suppose that $a_{1}=1$, and that for all positive integers $n, a_{n+1}=$ $\\left\\lceil\\sqrt{a_{n}^{2}+34}\\right\\rceil$. Compute the least value of $n$ such that $a_{n}>100 T$.", "answer": "491"}, {"index": 378, "question": "Compute the smallest $n$ such that in the regular $n$-gon $A_{1} A_{2} A_{3} \\cdots A_{n}, \\mathrm{~m} \\angle A_{1} A_{20} A_{13}<60^{\\circ}$.", "answer": "37"}, {"index": 379, "question": "Let $T=37$. A cube has edges of length $T$. Square holes of side length 1 are drilled from the center of each face of the cube through the cube's center and across to the opposite face; the edges of each hole are parallel to the edges of the cube. Compute the surface area of the resulting solid.", "answer": "8640"}, {"index": 380, "question": "Let $T=8640$. Compute $\\left\\lfloor\\log _{4}\\left(1+2+4+\\cdots+2^{T}\\right)\\right\\rfloor$.", "answer": "4320"}, {"index": 381, "question": "In ARMLopolis, every house number is a positive integer, and City Hall's address is 0. However, due to the curved nature of the cowpaths that eventually became the streets of ARMLopolis, the distance $d(n)$ between house $n$ and City Hall is not simply the value of $n$. Instead, if $n=3^{k} n^{\\prime}$, where $k \\geq 0$ is an integer and $n^{\\prime}$ is an integer not divisible by 3 , then $d(n)=3^{-k}$. For example, $d(18)=1 / 9$ and $d(17)=1$. Notice that even though no houses have negative numbers, $d(n)$ is well-defined for negative values of $n$. For example, $d(-33)=1 / 3$ because $-33=3^{1} \\cdot-11$. By definition, $d(0)=0$. Following the dictum \"location, location, location,\" this Power Question will refer to \"houses\" and \"house numbers\" interchangeably.\n\nCuriously, the arrangement of the houses is such that the distance from house $n$ to house $m$, written $d(m, n)$, is simply $d(m-n)$. For example, $d(3,4)=d(-1)=1$ because $-1=3^{0} \\cdot-1$. In particular, if $m=n$, then $d(m, n)=0$.\nCompute $d(6), d(16)$, and $d(72)$.", "answer": "\\frac{1}{3},1,\\frac{1}{9}"}, {"index": 382, "question": "In ARMLopolis, every house number is a positive integer, and City Hall's address is 0. However, due to the curved nature of the cowpaths that eventually became the streets of ARMLopolis, the distance $d(n)$ between house $n$ and City Hall is not simply the value of $n$. Instead, if $n=3^{k} n^{\\prime}$, where $k \\geq 0$ is an integer and $n^{\\prime}$ is an integer not divisible by 3 , then $d(n)=3^{-k}$. For example, $d(18)=1 / 9$ and $d(17)=1$. Notice that even though no houses have negative numbers, $d(n)$ is well-defined for negative values of $n$. For example, $d(-33)=1 / 3$ because $-33=3^{1} \\cdot-11$. By definition, $d(0)=0$. Following the dictum \"location, location, location,\" this Power Question will refer to \"houses\" and \"house numbers\" interchangeably.\n\nCuriously, the arrangement of the houses is such that the distance from house $n$ to house $m$, written $d(m, n)$, is simply $d(m-n)$. For example, $d(3,4)=d(-1)=1$ because $-1=3^{0} \\cdot-1$. In particular, if $m=n$, then $d(m, n)=0$.\nOf the houses with positive numbers less than 100, find, with proof, the house or houses which is (are) closest to City Hall.", "answer": "81"}, {"index": 383, "question": "In ARMLopolis, every house number is a positive integer, and City Hall's address is 0. However, due to the curved nature of the cowpaths that eventually became the streets of ARMLopolis, the distance $d(n)$ between house $n$ and City Hall is not simply the value of $n$. Instead, if $n=3^{k} n^{\\prime}$, where $k \\geq 0$ is an integer and $n^{\\prime}$ is an integer not divisible by 3 , then $d(n)=3^{-k}$. For example, $d(18)=1 / 9$ and $d(17)=1$. Notice that even though no houses have negative numbers, $d(n)$ is well-defined for negative values of $n$. For example, $d(-33)=1 / 3$ because $-33=3^{1} \\cdot-11$. By definition, $d(0)=0$. Following the dictum \"location, location, location,\" this Power Question will refer to \"houses\" and \"house numbers\" interchangeably.\n\nCuriously, the arrangement of the houses is such that the distance from house $n$ to house $m$, written $d(m, n)$, is simply $d(m-n)$. For example, $d(3,4)=d(-1)=1$ because $-1=3^{0} \\cdot-1$. In particular, if $m=n$, then $d(m, n)=0$.\n\n\nThe neighborhood of a house $n$, written $\\mathcal{N}(n)$, is the set of all houses that are the same distance from City Hall as $n$. In symbols, $\\mathcal{N}(n)=\\{m \\mid d(m)=d(n)\\}$. Geometrically, it may be helpful to think of $\\mathcal{N}(n)$ as a circle centered at City Hall with radius $d(n)$.\nSuppose that $n$ is a house with $d(n)=1 / 27$. Determine the ten smallest positive integers $m$ (in the standard ordering of the integers) such that $m \\in \\mathcal{N}(n)$.", "answer": "27,54,108,135,189,216,270,297,351,378"}, {"index": 384, "question": "In ARMLopolis, every house number is a positive integer, and City Hall's address is 0. However, due to the curved nature of the cowpaths that eventually became the streets of ARMLopolis, the distance $d(n)$ between house $n$ and City Hall is not simply the value of $n$. Instead, if $n=3^{k} n^{\\prime}$, where $k \\geq 0$ is an integer and $n^{\\prime}$ is an integer not divisible by 3 , then $d(n)=3^{-k}$. For example, $d(18)=1 / 9$ and $d(17)=1$. Notice that even though no houses have negative numbers, $d(n)$ is well-defined for negative values of $n$. For example, $d(-33)=1 / 3$ because $-33=3^{1} \\cdot-11$. By definition, $d(0)=0$. Following the dictum \"location, location, location,\" this Power Question will refer to \"houses\" and \"house numbers\" interchangeably.\n\nCuriously, the arrangement of the houses is such that the distance from house $n$ to house $m$, written $d(m, n)$, is simply $d(m-n)$. For example, $d(3,4)=d(-1)=1$ because $-1=3^{0} \\cdot-1$. In particular, if $m=n$, then $d(m, n)=0$.\n\n\nThe neighborhood of a house $n$, written $\\mathcal{N}(n)$, is the set of all houses that are the same distance from City Hall as $n$. In symbols, $\\mathcal{N}(n)=\\{m \\mid d(m)=d(n)\\}$. Geometrically, it may be helpful to think of $\\mathcal{N}(n)$ as a circle centered at City Hall with radius $d(n)$.\nSuppose that $d(17, m)=1 / 81$. Determine the possible values of $d(16, m)$.", "answer": "1"}, {"index": 385, "question": "In ARMLopolis, every house number is a positive integer, and City Hall's address is 0. However, due to the curved nature of the cowpaths that eventually became the streets of ARMLopolis, the distance $d(n)$ between house $n$ and City Hall is not simply the value of $n$. Instead, if $n=3^{k} n^{\\prime}$, where $k \\geq 0$ is an integer and $n^{\\prime}$ is an integer not divisible by 3 , then $d(n)=3^{-k}$. For example, $d(18)=1 / 9$ and $d(17)=1$. Notice that even though no houses have negative numbers, $d(n)$ is well-defined for negative values of $n$. For example, $d(-33)=1 / 3$ because $-33=3^{1} \\cdot-11$. By definition, $d(0)=0$. Following the dictum \"location, location, location,\" this Power Question will refer to \"houses\" and \"house numbers\" interchangeably.\n\nCuriously, the arrangement of the houses is such that the distance from house $n$ to house $m$, written $d(m, n)$, is simply $d(m-n)$. For example, $d(3,4)=d(-1)=1$ because $-1=3^{0} \\cdot-1$. In particular, if $m=n$, then $d(m, n)=0$.\n\n\nThe neighborhood of a house $n$, written $\\mathcal{N}(n)$, is the set of all houses that are the same distance from City Hall as $n$. In symbols, $\\mathcal{N}(n)=\\{m \\mid d(m)=d(n)\\}$. Geometrically, it may be helpful to think of $\\mathcal{N}(n)$ as a circle centered at City Hall with radius $d(n)$.\n\n\nUnfortunately for new development, ARMLopolis is full: every nonnegative integer corresponds to (exactly one) house (or City Hall, in the case of 0). However, eighteen families arrive and are looking to move in. After much debate, the connotations of using negative house numbers are deemed unacceptable, and the city decides on an alternative plan. On July 17, Shewad Movers arrive and relocate every family from house $n$ to house $n+18$, for all positive $n$ (so that City Hall does not move). For example, the family in house number 17 moves to house number 35.\nRoss takes a walk starting at his house, which is number 34 . He first visits house $n_{1}$, such that $d\\left(n_{1}, 34\\right)=1 / 3$. He then goes to another house, $n_{2}$, such that $d\\left(n_{1}, n_{2}\\right)=1 / 3$. Continuing in that way, he visits houses $n_{3}, n_{4}, \\ldots$, and each time, $d\\left(n_{i}, n_{i+1}\\right)=1 / 3$. At the end of the day, what is his maximum possible distance from his original house? Justify your answer.", "answer": "1/3"}, {"index": 386, "question": "In ARMLopolis, every house number is a positive integer, and City Hall's address is 0. However, due to the curved nature of the cowpaths that eventually became the streets of ARMLopolis, the distance $d(n)$ between house $n$ and City Hall is not simply the value of $n$. Instead, if $n=3^{k} n^{\\prime}$, where $k \\geq 0$ is an integer and $n^{\\prime}$ is an integer not divisible by 3 , then $d(n)=3^{-k}$. For example, $d(18)=1 / 9$ and $d(17)=1$. Notice that even though no houses have negative numbers, $d(n)$ is well-defined for negative values of $n$. For example, $d(-33)=1 / 3$ because $-33=3^{1} \\cdot-11$. By definition, $d(0)=0$. Following the dictum \"location, location, location,\" this Power Question will refer to \"houses\" and \"house numbers\" interchangeably.\n\nCuriously, the arrangement of the houses is such that the distance from house $n$ to house $m$, written $d(m, n)$, is simply $d(m-n)$. For example, $d(3,4)=d(-1)=1$ because $-1=3^{0} \\cdot-1$. In particular, if $m=n$, then $d(m, n)=0$.\n\n\nThe neighborhood of a house $n$, written $\\mathcal{N}(n)$, is the set of all houses that are the same distance from City Hall as $n$. In symbols, $\\mathcal{N}(n)=\\{m \\mid d(m)=d(n)\\}$. Geometrically, it may be helpful to think of $\\mathcal{N}(n)$ as a circle centered at City Hall with radius $d(n)$.\n\nLater, ARMLopolis finally decides on a drastic expansion plan: now house numbers will be rational numbers. To define $d(p / q)$, with $p$ and $q$ integers such that $p q \\neq 0$, write $p / q=3^{k} p^{\\prime} / q^{\\prime}$, where neither $p^{\\prime}$ nor $q^{\\prime}$ is divisible by 3 and $k$ is an integer (not necessarily positive); then $d(p / q)=3^{-k}$.\nCompute $d(3 / 5), d(5 / 8)$, and $d(7 / 18)$.", "answer": "\\frac{1}{3},1,9"}, {"index": 387, "question": "Let $A R M L$ be a trapezoid with bases $\\overline{A R}$ and $\\overline{M L}$, such that $M R=R A=A L$ and $L R=$ $A M=M L$. Point $P$ lies inside the trapezoid such that $\\angle R M P=12^{\\circ}$ and $\\angle R A P=6^{\\circ}$. Diagonals $A M$ and $R L$ intersect at $D$. Compute the measure, in degrees, of angle $A P D$.", "answer": "48"}, {"index": 388, "question": "A regular hexagon has side length 1. Compute the average of the areas of the 20 triangles whose vertices are vertices of the hexagon.", "answer": "\\frac{9\\sqrt{3}}{20}"}, {"index": 389, "question": "<PERSON> was planning to buy 20 items from the ARML shop. He wanted some mugs, which cost $\\$ 10$ each, and some shirts, which cost $\\$ 6$ each. After checking his wallet he decided to put $40 \\%$ of the mugs back. Compute the number of dollars he spent on the remaining items.", "answer": "120"}, {"index": 390, "question": "Let $x$ be the smallest positive integer such that $1584 \\cdot x$ is a perfect cube, and let $y$ be the smallest positive integer such that $x y$ is a multiple of 1584 . Compute $y$.", "answer": "12"}, {"index": 391, "question": "<PERSON> goes to the store to buy apples and peaches. She buys five of each, hands the shopkeeper one $\\$ 5$ bill, but then has to give the shopkeeper another; she gets back some change. <PERSON> goes to the same store, buys 2 apples and 12 peaches, and tries to pay with a single $\\$ 10$ bill. But that's not enough, so <PERSON> has to give the shopkeeper another $\\$ 10$ bill, and also gets some change. Finally, <PERSON> goes to the same store to buy 25 peaches. Assuming that the price in cents of each fruit is an integer, compute the least amount of money, in cents, that <PERSON> can expect to pay.", "answer": "1525"}, {"index": 392, "question": "Circle $O$ has radius 6. Point $P$ lies outside circle $O$, and the shortest distance from $P$ to circle $O$ is 4. Chord $\\overline{A B}$ is parallel to $\\overleftrightarrow{O P}$, and the distance between $\\overline{A B}$ and $\\overleftrightarrow{O P}$ is 2 . Compute $P A^{2}+P B^{2}$.", "answer": "272"}, {"index": 393, "question": "A palindrome is a positive integer, not ending in 0 , that reads the same forwards and backwards. For example, 35253,171,44, and 2 are all palindromes, but 17 and 1210 are not. Compute the least positive integer greater than 2013 that cannot be written as the sum of two palindromes.", "answer": "2019"}, {"index": 394, "question": "Positive integers $x, y, z$ satisfy $x y+z=160$. Compute the smallest possible value of $x+y z$.", "answer": "50"}, {"index": 395, "question": "Compute $\\cos ^{3} \\frac{2 \\pi}{7}+\\cos ^{3} \\frac{4 \\pi}{7}+\\cos ^{3} \\frac{8 \\pi}{7}$.", "answer": "-\\frac{1}{2}"}, {"index": 396, "question": "In right triangle $A B C$ with right angle $C$, line $\\ell$ is drawn through $C$ and is parallel to $\\overline{A B}$. Points $P$ and $Q$ lie on $\\overline{A B}$ with $P$ between $A$ and $Q$, and points $R$ and $S$ lie on $\\ell$ with $C$ between $R$ and $S$ such that $P Q R S$ is a square. Let $\\overline{P S}$ intersect $\\overline{A C}$ in $X$, and let $\\overline{Q R}$ intersect $\\overline{B C}$ in $Y$. The inradius of triangle $A B C$ is 10 , and the area of square $P Q R S$ is 576 . Compute the sum of the inradii of triangles $A X P, C X S, C Y R$, and $B Y Q$.", "answer": "14"}, {"index": 397, "question": "Compute the sum of all real numbers $x$ such that\n\n$$\n\\left\\lfloor\\frac{x}{2}\\right\\rfloor-\\left\\lfloor\\frac{x}{3}\\right\\rfloor=\\frac{x}{7}\n$$", "answer": "-21"}, {"index": 398, "question": "Let $S=\\{1,2, \\ldots, 20\\}$, and let $f$ be a function from $S$ to $S$; that is, for all $s \\in S, f(s) \\in S$. Define the sequence $s_{1}, s_{2}, s_{3}, \\ldots$ by setting $s_{n}=\\sum_{k=1}^{20} \\underbrace{(f \\circ \\cdots \\circ f)}_{n}(k)$. That is, $s_{1}=f(1)+$ $\\cdots+f(20), s_{2}=f(f(1))+\\cdots+f(f(20)), s_{3}=f(f(f(1)))+f(f(f(2)))+\\cdots+f(f(f(20)))$, etc. Compute the smallest integer $p$ such that the following statement is true: The sequence $s_{1}, s_{2}, s_{3}, \\ldots$ must be periodic after a certain point, and its period is at most $p$. (If the sequence is never periodic, then write $\\infty$ as your answer.)", "answer": "140"}, {"index": 399, "question": "Compute the smallest positive integer $n$ such that $n^{2}+n^{0}+n^{1}+n^{3}$ is a multiple of 13 .", "answer": "5"}, {"index": 400, "question": "Let $T=T N Y W R$. Compute $2^{\\log _{T} 8}-8^{\\log _{T} 2}$.", "answer": "0"}, {"index": 401, "question": "Let $T=T N Y W R$. At some point during a given week, a law enforcement officer had issued $T+2$ traffic warnings, 20 tickets, and had made $T+5$ arrests. How many more tickets must the officer issue in order for the combined number of tickets and arrests to be 20 times the number of warnings issued that week?", "answer": "15"}, {"index": 402, "question": "$\\quad$ Let $T=T N Y W R$. In parallelogram $A R M L$, points $P$ and $Q$ trisect $\\overline{A R}$ and points $W, X, Y, Z$ divide $\\overline{M L}$ into fifths (where $W$ is closest to $M$, and points $X$ and $Y$ are both between $W$ and $Z$ ). If $[A R M L]=T$, compute $[P Q W Z]$.", "answer": "7"}, {"index": 403, "question": "Let $T=T N Y W R$. Compute the number of positive perfect cubes that are divisors of $(T+10) !$.", "answer": "36"}, {"index": 404, "question": "Let $T=T N Y W R$. The graph of $y=x^{2}+2 x-T$ intersects the $x$-axis at points $A$ and $M$, which are diagonally opposite vertices of square $A R M L$. Compute $[A R M L]$.", "answer": "74"}, {"index": 405, "question": "Let $S$ be the set of prime factors of the numbers you receive from positions 7 and 9 , and let $p$ and $q$ be the two least distinct elements of $S$, with $p<q$. Hexagon HEXAGO is inscribed in circle $\\omega$, and every angle of $H E X A G O$ is $120^{\\circ}$. If $H E=X A=G O=p$ and $E X=A G=O H=q$, compute the area of circle $\\omega$.", "answer": "\\frac{67\\pi}{3}"}, {"index": 406, "question": "Let $T=T N Y W R$. A group of $n$ friends goes camping; two of them are selected to set up the campsite when they arrive and two others are selected to take down the campsite the next day. Compute the smallest possible value of $n$ such that there are at least $T$ ways of selecting the four helpers.", "answer": "7"}, {"index": 407, "question": "Let $T=T N Y W R$. The parabola $y=x^{2}+T x$ is tangent to the parabola $y=-(x-2 T)^{2}+b$. Compute $b$.", "answer": "184"}, {"index": 408, "question": "Let $T=T N Y W R$. The first two terms of a sequence are $a_{1}=3 / 5$ and $a_{2}=4 / 5$. For $n>2$, if $n$ is odd, then $a_{n}=a_{n-1}^{2}-a_{n-2}^{2}$, while if $n$ is even, then $a_{n}=2 a_{n-2} a_{n-3}$. Compute the sum of the squares of the first $T-3$ terms of the sequence.", "answer": "8"}, {"index": 409, "question": "Let $T=T N Y W R$. A regular $n$-gon has exactly $T$ more diagonals than a regular $(n-1)$-gon. Compute the value of $n$.", "answer": "19"}, {"index": 410, "question": "Let $T=T N Y W R$. The sequence $a_{1}, a_{2}, a_{3}, \\ldots$, is arithmetic with $a_{16}=13$ and $a_{30}=20$. Compute the value of $k$ for which $a_{k}=T$.", "answer": "17"}, {"index": 411, "question": "Let $T=T N Y W R$. A rectangular prism has a length of 1 , a width of 3 , a height of $h$, and has a total surface area of $T$. Compute the value of $h$.", "answer": "\\frac{27}{2}"}, {"index": 412, "question": "The zeros of $x^{2}+b x+93$ are $r$ and $s$. If the zeros of $x^{2}-22 x+c$ are $r+1$ and $s+1$, compute $c$.", "answer": "114"}, {"index": 413, "question": "Let $N=888,888 \\times 9,999,999$. Compute the sum of the digits of $N$.", "answer": "63"}, {"index": 414, "question": "Five equilateral triangles are drawn in the plane so that no two sides of any of the triangles are parallel. Compute the maximum number of points of intersection among all five triangles.", "answer": "60"}, {"index": 415, "question": "$\\quad$ Let $S$ be the set of four-digit positive integers for which the sum of the squares of their digits is 17 . For example, $2023 \\in S$ because $2^{2}+0^{2}+2^{2}+3^{2}=17$. Compute the median of $S$.", "answer": "2302"}, {"index": 416, "question": "Let $E U C L I D$ be a hexagon inscribed in a circle of radius 5 . Given that $E U=U C=L I=I D=6$, and $C L=D E$, compute $C L$.", "answer": "\\frac{14}{5}"}, {"index": 417, "question": "The ARMLLexicon consists of 10 letters: $\\{A, R, M, L, e, x, i, c, o, n\\}$. A palindrome is an ordered list of letters that read the same backwards and forwards; for example, MALAM, n, oncecno, and MoM are palindromes. Compute the number of 15-letter palindromes that can be spelled using letters in the ARMLLexicon, among which there are four consecutive letters that spell out $A R M L$.", "answer": "99956"}, {"index": 418, "question": "Let $10^{y}$ be the product of all real numbers $x$ such that $\\log x=\\frac{3+\\left\\lfloor(\\log x)^{2}\\right\\rfloor}{4}$. Compute $y$.", "answer": "8"}, {"index": 419, "question": "The solutions to the equation $x^{2}-180 x+8=0$ are $r_{1}$ and $r_{2}$. Compute\n\n$$\n\\frac{r_{1}}{\\sqrt[3]{r_{2}}}+\\frac{r_{2}}{\\sqrt[3]{r_{1}}}\n$$", "answer": "508"}, {"index": 420, "question": "Circle $\\omega$ is tangent to parallel lines $\\ell_{1}$ and $\\ell_{2}$ at $A$ and $B$ respectively. Circle $\\omega_{1}$ is tangent to $\\ell_{1}$ at $C$ and to $\\omega$ externally at $P$. Circle $\\omega_{2}$ is tangent to $\\ell_{2}$ at $D$ and to $\\omega$ externally at $Q$. Circles $\\omega_{1}$ and $\\omega_{2}$ are also externally tangent to each other. Given that $A Q=12$ and $D Q=8$, compute $C D$.", "answer": "5\\sqrt{10}"}, {"index": 421, "question": "Given quadrilateral $A R M L$ with $A R=20, R M=23, M L=25$, and $A M=32$, compute the number of different integers that could be the perimeter of $A R M L$.", "answer": "49"}, {"index": 422, "question": "Let $\\mathcal{S}$ denote the set of all real polynomials $A(x)$ with leading coefficient 1 such that there exists a real polynomial $B(x)$ that satisfies\n\n$$\n\\frac{1}{A(x)}+\\frac{1}{B(x)}+\\frac{1}{x+10}=\\frac{1}{x}\n$$\n\nfor all real numbers $x$ for which $A(x) \\neq 0, B(x) \\neq 0$, and $x \\neq-10,0$. Compute $\\sum_{A \\in \\mathcal{S}} A(10)$.", "answer": "46760"}, {"index": 423, "question": "Let $T=688$. Let $a$ be the least nonzero digit in $T$, and let $b$ be the greatest digit in $T$. In square $N O R M, N O=b$, and points $P_{1}$ and $P_{2}$ lie on $\\overline{N O}$ and $\\overline{O R}$, respectively, so that $O P_{1}=O P_{2}=a$. A circle centered at $O$ has radius $a$, and quarter-circular arc $\\widehat{P_{1} P_{2}}$ is drawn. There is a circle that is tangent to $\\widehat{P_{1} P_{2}}$ and to sides $\\overline{M N}$ and $\\overline{M R}$. The radius of this circle can be written in the form $x-y \\sqrt{2}$, where $x$ and $y$ are positive integers. Compute $x+y$.", "answer": "36"}, {"index": 424, "question": "Let $T=36$. Square $A B C D$ has area $T$. Points $M, N, O$, and $P$ lie on $\\overline{A B}$, $\\overline{B C}, \\overline{C D}$, and $\\overline{D A}$, respectively, so that quadrilateral $M N O P$ is a rectangle with $M P=2$. Compute $M N$.", "answer": "6\\sqrt{2}-2"}, {"index": 425, "question": "In a game, a player chooses 2 of the 13 letters from the first half of the alphabet (i.e., A-M) and 2 of the 13 letters from the second half of the alphabet (i.e., N-Z). <PERSON><PERSON><PERSON> plays the game, and then <PERSON><PERSON> plays the game. Compute the probability that <PERSON><PERSON><PERSON> and <PERSON><PERSON> choose the same set of four letters.", "answer": "\\frac{1}{6084}"}, {"index": 426, "question": "Let $T=\\frac{1}{6084}$. Compute the least positive integer $n$ such that when a fair coin is flipped $n$ times, the probability of it landing heads on all $n$ flips is less than $T$.", "answer": "13"}, {"index": 427, "question": "Let $T=13$. Compute the least integer $n>2023$ such that the equation $x^{2}-T x-n=0$ has integer solutions.", "answer": "2028"}, {"index": 428, "question": "In a sequence of $n$ consecutive positive integers, where $n>1$, an element of the sequence is said to be cromulent if it is relatively prime to all other numbers in the sequence. Every element of a sequence with $n=2$ is cromulent because any two consecutive integers are relatively prime to each other.\nFind the maximum and minimum possible number of cromulent elements in a sequence of $n$ consecutive positive integers with $n=6$;", "answer": "1,2"}, {"index": 429, "question": "In a sequence of $n$ consecutive positive integers, where $n>1$, an element of the sequence is said to be cromulent if it is relatively prime to all other numbers in the sequence. Every element of a sequence with $n=2$ is cromulent because any two consecutive integers are relatively prime to each other.\nFind the maximum and minimum possible number of cromulent elements in a sequence of $n$ consecutive positive integers with $n=7$.", "answer": "1,3"}, {"index": 430, "question": "For an integer $n \\geq 4$, define $a_{n}$ to be the product of all real numbers that are roots to at least one quadratic polynomial whose coefficients are positive integers that sum to $n$. Compute\n\n$$\n\\frac{a_{4}}{a_{5}}+\\frac{a_{5}}{a_{6}}+\\frac{a_{6}}{a_{7}}+\\cdots+\\frac{a_{2022}}{a_{2023}} .\n$$", "answer": "-2019"}, {"index": 431, "question": "Suppose that $u$ and $v$ are distinct numbers chosen at random from the set $\\{1,2,3, \\ldots, 30\\}$. Compute the probability that the roots of the polynomial $(x+u)(x+v)+4$ are integers.", "answer": "\\frac{17}{145}"}, {"index": 432, "question": "The degree-measures of the interior angles of convex hexagon TIEBRK are all integers in arithmetic progression. Compute the least possible degree-measure of the smallest interior angle in hexagon TIEBRK.", "answer": "65"}, {"index": 433, "question": "A six-digit natural number is \"sort-of-decreasing\" if its first three digits are in strictly decreasing order and its last three digits are in strictly decreasing order. For example, 821950 and 631631 are sort-of-decreasing but 853791 and 911411 are not. Compute the number of sort-of-decreasing six-digit natural numbers.", "answer": "14400"}, {"index": 434, "question": "For each positive integer $N$, let $P(N)$ denote the product of the digits of $N$. For example, $P(8)=8$, $P(451)=20$, and $P(2023)=0$. Compute the least positive integer $n$ such that $P(n+23)=P(n)+23$.", "answer": "34"}, {"index": 435, "question": "Compute the least integer value of the function\n\n$$\nf(x)=\\frac{x^{4}-6 x^{3}+2 x^{2}-6 x+2}{x^{2}+1}\n$$\n\nwhose domain is the set of all real numbers.", "answer": "-7"}, {"index": 436, "question": "Suppose that noncongruent triangles $A B C$ and $X Y Z$ are given such that $A B=X Y=10, B C=$ $Y Z=9$, and $\\mathrm{m} \\angle C A B=\\mathrm{m} \\angle Z X Y=30^{\\circ}$. Compute $[A B C]+[X Y Z]$.", "answer": "25\\sqrt{3}"}, {"index": 437, "question": "The mean, median, and unique mode of a list of positive integers are three consecutive integers in some order. Compute the least possible sum of the integers in the original list.", "answer": "12"}, {"index": 438, "question": "<PERSON> builds a circular table; he then carves one or more positive integers into the table at points equally spaced around its circumference. He considers two tables to be the same if one can be rotated so that it has the same numbers in the same positions as the other. For example, a table with the numbers $8,4,5$ (in clockwise order) is considered the same as a table with the numbers 4, 5,8 (in clockwise order), but both tables are different from a table with the numbers 8, 5, 4 (in clockwise order). Given that the numbers he carves sum to 17 , compute the number of different tables he can make.", "answer": "7711"}, {"index": 439, "question": "In quadrilateral $A B C D, \\mathrm{~m} \\angle B+\\mathrm{m} \\angle D=270^{\\circ}$. The circumcircle of $\\triangle A B D$ intersects $\\overline{C D}$ at point $E$, distinct from $D$. Given that $B C=4, C E=5$, and $D E=7$, compute the diameter of the circumcircle of $\\triangle A B D$.", "answer": "\\sqrt{130}"}, {"index": 440, "question": "Let $i=\\sqrt{-1}$. The complex number $z=-142+333 \\sqrt{5} i$ can be expressed as a product of two complex numbers in multiple different ways, two of which are $(57-8 \\sqrt{5} i)(-6+5 \\sqrt{5} i)$ and $(24+\\sqrt{5} i)(-3+14 \\sqrt{5} i)$. Given that $z=-142+333 \\sqrt{5} i$ can be written as $(a+b \\sqrt{5} i)(c+d \\sqrt{5} i)$, where $a, b, c$, and $d$ are positive integers, compute the lesser of $a+b$ and $c+d$.", "answer": "17"}, {"index": 441, "question": "Parallelogram $A B C D$ is rotated about $A$ in the plane, resulting in $A B^{\\prime} C^{\\prime} D^{\\prime}$, with $D$ on $\\overline{A B^{\\prime}}$. Suppose that $\\left[B^{\\prime} C D\\right]=\\left[A B D^{\\prime}\\right]=\\left[B C C^{\\prime}\\right]$. Compute $\\tan \\angle A B D$.", "answer": "\\sqrt{2}-1,\\frac{3-\\sqrt{2}}{7}"}, {"index": 442, "question": "Compute the least integer greater than 2023 , the sum of whose digits is 17 .", "answer": "2069"}, {"index": 443, "question": "Let $T$ = 2069, and let $K$ be the sum of the digits of $T$. Let $r$ and $s$ be the two roots of the polynomial $x^{2}-18 x+K$. Compute $|r-s|$.", "answer": "16"}, {"index": 444, "question": "Let $T=$ 7, and let $K=9 T$. Let $A_{1}=2$, and for $n \\geq 2$, let\n\n$$\nA_{n}= \\begin{cases}A_{n-1}+1 & \\text { if } n \\text { is not a perfect square } \\\\ \\sqrt{n} & \\text { if } n \\text { is a perfect square. }\\end{cases}\n$$\n\nCompute $A_{K}$.", "answer": "21"}, {"index": 445, "question": "Let $T=$ 21. The number $20^{T} \\cdot 23^{T}$ has $K$ positive divisors. Compute the greatest prime factor of $K$.", "answer": "43"}, {"index": 446, "question": "Let $T=43$. Compute the positive integer $n \\neq 17$ for which $\\left(\\begin{array}{c}T-3 \\\\ 17\\end{array}\\right)=\\left(\\begin{array}{c}T-3 \\\\ n\\end{array}\\right)$.", "answer": "23"}, {"index": 447, "question": "Let $T=23$ . Compute the units digit of $T^{2023}+T^{20}-T^{23}$.", "answer": "1"}, {"index": 448, "question": "Let $T=$ 3. Suppose that $T$ fair coins are flipped. Compute the probability that at least one tails is flipped.", "answer": "\\frac{7}{8}"}, {"index": 449, "question": "Let $T=$ $\\frac{7}{8}$. The number $T$ can be expressed as a reduced fraction $\\frac{m}{n}$, where $m$ and $n$ are positive integers whose greatest common divisor is 1 . The equation $x^{2}+(m+n) x+m n=0$ has two distinct real solutions. Compute the lesser of these two solutions.", "answer": "-8"}, {"index": 450, "question": "Let $T=$ -8, and let $i=\\sqrt{-1}$. Compute the positive integer $k$ for which $(-1+i)^{k}=\\frac{1}{2^{T}}$.", "answer": "16"}, {"index": 451, "question": "Let $T=$ 16. Compute the value of $x$ that satisfies $\\log _{4} T=\\log _{2} x$.", "answer": "4"}, {"index": 452, "question": "Let $T=$ 4. Pyramid $L E O J S$ is a right square pyramid with base $E O J S$, whose area is $T$. Given that $L E=5 \\sqrt{2}$, compute $[L E O]$.", "answer": "7"}, {"index": 453, "question": "Let $T=$ 7. Compute the units digit of $T^{2023}+(T-2)^{20}-(T+10)^{23}$.", "answer": "5"}, {"index": 454, "question": "Let $r=1$ and $R=5$. A circle with radius $r$ is centered at $A$, and a circle with radius $R$ is centered at $B$. The two circles are internally tangent. Point $P$ lies on the smaller circle so that $\\overline{B P}$ is tangent to the smaller circle. Compute $B P$.", "answer": "\\sqrt{15}"}, {"index": 455, "question": "Compute the largest prime divisor of $15 !-13$ !.", "answer": "19"}, {"index": 456, "question": "Three non-overlapping squares of positive integer side lengths each have one vertex at the origin and sides parallel to the coordinate axes. Together, the three squares enclose a region whose area is 41 . Compute the largest possible perimeter of the region.", "answer": "32"}, {"index": 457, "question": "A circle with center $O$ and radius 1 contains chord $\\overline{A B}$ of length 1 , and point $M$ is the midpoint of $\\overline{A B}$. If the perpendicular to $\\overline{A O}$ through $M$ intersects $\\overline{A O}$ at $P$, compute $[M A P]$.", "answer": "\\frac{\\sqrt{3}}{32}"}, {"index": 458, "question": "$\\quad$ Suppose that $p$ and $q$ are two-digit prime numbers such that $p^{2}-q^{2}=2 p+6 q+8$. Compute the largest possible value of $p+q$.", "answer": "162"}, {"index": 459, "question": "The four zeros of the polynomial $x^{4}+j x^{2}+k x+225$ are distinct real numbers in arithmetic progression. Compute the value of $j$.", "answer": "-50"}, {"index": 460, "question": "Compute the smallest positive integer $n$ such that\n\n$$\nn,\\lfloor\\sqrt{n}\\rfloor,\\lfloor\\sqrt[3]{n}\\rfloor,\\lfloor\\sqrt[4]{n}\\rfloor,\\lfloor\\sqrt[5]{n}\\rfloor,\\lfloor\\sqrt[6]{n}\\rfloor,\\lfloor\\sqrt[7]{n}\\rfloor, \\text { and }\\lfloor\\sqrt[8]{n}\\rfloor\n$$\n\nare distinct.", "answer": "4096"}, {"index": 461, "question": "If $n$ is a positive integer, then $n$ !! is defined to be $n(n-2)(n-4) \\cdots 2$ if $n$ is even and $n(n-2)(n-4) \\cdots 1$ if $n$ is odd. For example, $8 ! !=8 \\cdot 6 \\cdot 4 \\cdot 2=384$ and $9 ! !=9 \\cdot 7 \\cdot 5 \\cdot 3 \\cdot 1=945$. Compute the number of positive integers $n$ such that $n !$ ! divides 2012!!.", "answer": "1510"}, {"index": 462, "question": "On the complex plane, the parallelogram formed by the points $0, z, \\frac{1}{z}$, and $z+\\frac{1}{z}$ has area $\\frac{35}{37}$, and the real part of $z$ is positive. If $d$ is the smallest possible value of $\\left|z+\\frac{1}{z}\\right|$, compute $d^{2}$.", "answer": "\\frac{50}{37}"}, {"index": 463, "question": "One face of a $2 \\times 2 \\times 2$ cube is painted (not the entire cube), and the cube is cut into eight $1 \\times 1 \\times 1$ cubes. The small cubes are reassembled randomly into a $2 \\times 2 \\times 2$ cube. Compute the probability that no paint is showing.", "answer": "\\frac{1}{16}"}, {"index": 464, "question": "In triangle $A B C, A B=B C$. A trisector of $\\angle B$ intersects $\\overline{A C}$ at $D$. If $A B, A C$, and $B D$ are integers and $A B-B D=7$, compute $A C$.", "answer": "146"}, {"index": 465, "question": "The rational number $r$ is the largest number less than 1 whose base-7 expansion consists of two distinct repeating digits, $r=0 . \\underline{A} \\underline{B} \\underline{A} \\underline{B} \\underline{A} \\underline{B} \\ldots$ Written as a reduced fraction, $r=\\frac{p}{q}$. Compute $p+q$ (in base 10).", "answer": "95"}, {"index": 466, "question": "Let $T=95$. Triangle $A B C$ has $A B=A C$. Points $M$ and $N$ lie on $\\overline{B C}$ such that $\\overline{A M}$ and $\\overline{A N}$ trisect $\\angle B A C$, with $M$ closer to $C$. If $\\mathrm{m} \\angle A M C=T^{\\circ}$, then $\\mathrm{m} \\angle A C B=U^{\\circ}$. Compute $U$.", "answer": "75"}, {"index": 467, "question": "Let $T=75$. At Wash College of Higher Education (Wash Ed.), the entering class has $n$ students. Each day, two of these students are selected to oil the slide rules. If the entering class had two more students, there would be $T$ more ways of selecting the two slide rule oilers. Compute $n$.", "answer": "37"}, {"index": 468, "question": "Compute the least positive integer $n$ such that the set of angles\n\n$$\n\\left\\{123^{\\circ}, 246^{\\circ}, \\ldots, n \\cdot 123^{\\circ}\\right\\}\n$$\n\ncontains at least one angle in each of the four quadrants.", "answer": "11"}, {"index": 469, "question": "Let $T=11$. In ARMLvania, license plates use only the digits 1-9, and each license plate contains exactly $T-3$ digits. On each plate, all digits are distinct, and for all $k \\leq T-3$, the $k^{\\text {th }}$ digit is at least $k$. Compute the number of valid ARMLvanian license plates.", "answer": "256"}, {"index": 470, "question": "Let $T=256$. Let $\\mathcal{R}$ be the region in the plane defined by the inequalities $x^{2}+y^{2} \\geq T$ and $|x|+|y| \\leq \\sqrt{2 T}$. Compute the area of region $\\mathcal{R}$.", "answer": "1024-256\\pi"}, {"index": 471, "question": "Triangle $A B C$ has $\\mathrm{m} \\angle A>\\mathrm{m} \\angle B>\\mathrm{m} \\angle C$. The angle between the altitude and the angle bisector at vertex $A$ is $6^{\\circ}$. The angle between the altitude and the angle bisector at vertex $B$ is $18^{\\circ}$. Compute the degree measure of angle $C$.", "answer": "44^{\\circ}"}, {"index": 472, "question": "Compute the number of ordered pairs of integers $(b, c)$, with $-20 \\leq b \\leq 20,-20 \\leq c \\leq 20$, such that the equations $x^{2}+b x+c=0$ and $x^{2}+c x+b=0$ share at least one root.", "answer": "81"}, {"index": 473, "question": "A seventeen-sided die has faces numbered 1 through 17, but it is not fair: 17 comes up with probability $1 / 2$, and each of the numbers 1 through 16 comes up with probability $1 / 32$. Compute the probability that the sum of two rolls is either 20 or 12.", "answer": "\\frac{7}{128}"}, {"index": 474, "question": "Compute the number of ordered pairs of integers $(a, b)$ such that $1<a \\leq 50,1<b \\leq 50$, and $\\log _{b} a$ is rational.", "answer": "81"}, {"index": 475, "question": "Suppose that 5-letter \"words\" are formed using only the letters A, R, M, and L. Each letter need not be used in a word, but each word must contain at least two distinct letters. Compute the number of such words that use the letter A more than any other letter.", "answer": "165"}, {"index": 476, "question": "Positive integers $a_{1}, a_{2}, a_{3}, \\ldots$ form an arithmetic sequence. If $a_{1}=10$ and $a_{a_{2}}=100$, compute $a_{a_{a_{3}}}$.", "answer": "820"}, {"index": 477, "question": "The graphs of $y=x^{2}-|x|-12$ and $y=|x|-k$ intersect at distinct points $A, B, C$, and $D$, in order of increasing $x$-coordinates. If $A B=B C=C D$, compute $k$.", "answer": "10+2\\sqrt{2}"}, {"index": 478, "question": "The zeros of $f(x)=x^{6}+2 x^{5}+3 x^{4}+5 x^{3}+8 x^{2}+13 x+21$ are distinct complex numbers. Compute the average value of $A+B C+D E F$ over all possible permutations $(A, B, C, D, E, F)$ of these six numbers.", "answer": "-\\frac{23}{60}"}, {"index": 479, "question": "Let $N=\\left\\lfloor(3+\\sqrt{5})^{34}\\right\\rfloor$. Compute the remainder when $N$ is divided by 100 .", "answer": "47"}, {"index": 480, "question": "Let $A B C$ be a triangle with $\\mathrm{m} \\angle B=\\mathrm{m} \\angle C=80^{\\circ}$. Compute the number of points $P$ in the plane such that triangles $P A B, P B C$, and $P C A$ are all isosceles and non-degenerate. Note: the approximation $\\cos 80^{\\circ} \\approx 0.17$ may be useful.", "answer": "6"}, {"index": 481, "question": "If $\\lceil u\\rceil$ denotes the least integer greater than or equal to $u$, and $\\lfloor u\\rfloor$ denotes the greatest integer less than or equal to $u$, compute the largest solution $x$ to the equation\n\n$$\n\\left\\lfloor\\frac{x}{3}\\right\\rfloor+\\lceil 3 x\\rceil=\\sqrt{11} \\cdot x\n$$", "answer": "\\frac{189\\sqrt{11}}{11}"}, {"index": 482, "question": "If $x, y$, and $z$ are positive integers such that $x y=20$ and $y z=12$, compute the smallest possible value of $x+z$.", "answer": "8"}, {"index": 483, "question": "Let $T=8$. Let $A=(1,5)$ and $B=(T-1,17)$. Compute the value of $x$ such that $(x, 3)$ lies on the perpendicular bisector of $\\overline{A B}$.", "answer": "20"}, {"index": 484, "question": "Let T be a rational number. Let $N$ be the smallest positive $T$-digit number that is divisible by 33 . Compute the product of the last two digits of $N$.", "answer": "6"}, {"index": 485, "question": "Let $T=15$. For complex $z$, define the function $f_{1}(z)=z$, and for $n>1, f_{n}(z)=$ $f_{n-1}(\\bar{z})$. If $f_{1}(z)+2 f_{2}(z)+3 f_{3}(z)+4 f_{4}(z)+5 f_{5}(z)=T+T i$, compute $|z|$.", "answer": "\\sqrt{26}"}, {"index": 486, "question": "Let $T=\\sqrt{26}$. Compute the number of ordered pairs of positive integers $(a, b)$ with the property that $a b=T^{20} \\cdot 210^{12}$, and the greatest common divisor of $a$ and $b$ is 1 .", "answer": "32"}, {"index": 487, "question": "Let $T=32$. Given that $\\sin \\theta=\\frac{\\sqrt{T^{2}-64}}{T}$, compute the largest possible value of the infinite series $\\cos \\theta+\\cos ^{2} \\theta+\\cos ^{3} \\theta+\\ldots$.", "answer": "\\frac{1}{3}"}, {"index": 488, "question": "Let $T=\\frac{9}{17}$. When $T$ is expressed as a reduced fraction, let $m$ and $n$ be the numerator and denominator, respectively. A square pyramid has base $A B C D$, the distance from vertex $P$ to the base is $n-m$, and $P A=P B=P C=P D=n$. Compute the area of square $A B C D$.", "answer": "450"}, {"index": 489, "question": "Let $T=-14$, and let $d=|T|$. A person whose birthday falls between July 23 and August 22 inclusive is called a Leo. A person born in July is randomly selected, and it is given that her birthday is before the $d^{\\text {th }}$ day of July. Another person who was also born in July is randomly selected, and it is given that his birthday is after the $d^{\\text {th }}$ day of July. Compute the probability that exactly one of these people is a <PERSON>.", "answer": "\\frac{9}{17}"}, {"index": 490, "question": "Let $T=-10$. Given that $\\log _{2} 4^{8 !}+\\log _{4} 2^{8 !}=6 ! \\cdot T \\cdot x$, compute $x$.", "answer": "-14"}, {"index": 491, "question": "Let $T=20$. For some real constants $a$ and $b$, the solution sets of the equations $x^{2}+(5 b-T-a) x=T+1$ and $2 x^{2}+(T+8 a-2) x=-10 b$ are the same. Compute $a$.", "answer": "-10"}, {"index": 492, "question": "Let T be a rational number, and let $K=T-2$. If $K$ workers can produce 9 widgets in 1 hour, compute the number of workers needed to produce $\\frac{720}{K}$ widgets in 4 hours.", "answer": "20"}, {"index": 493, "question": "Let $T=2018$, and append the digits of $T$ to $\\underline{A} \\underline{A} \\underline{B}$ (for example, if $T=17$, then the result would be $\\underline{1} \\underline{\\underline{A}} \\underline{A} \\underline{B}$ ). If the resulting number is divisible by 11 , compute the largest possible value of $A+B$.", "answer": "14"}, {"index": 494, "question": "Given that April $1^{\\text {st }}, 2012$ fell on a Sunday, what is the next year in which April $1^{\\text {st }}$ will fall on a Sunday?", "answer": "2018"}, {"index": 495, "question": "Let $p$ be a prime number. If $p$ years ago, the ages of three children formed a geometric sequence with a sum of $p$ and a common ratio of 2 , compute the sum of the children's current ages.", "answer": "28"}, {"index": 496, "question": "Define a reverse prime to be a positive integer $N$ such that when the digits of $N$ are read in reverse order, the resulting number is a prime. For example, the numbers 5, 16, and 110 are all reverse primes. Compute the largest two-digit integer $N$ such that the numbers $N, 4 \\cdot N$, and $5 \\cdot N$ are all reverse primes.", "answer": "79"}, {"index": 497, "question": "Some students in a gym class are wearing blue jerseys, and the rest are wearing red jerseys. There are exactly 25 ways to pick a team of three players that includes at least one player wearing each color. Compute the number of students in the class.", "answer": "7"}, {"index": 498, "question": "Point $P$ is on the hypotenuse $\\overline{E N}$ of right triangle $B E N$ such that $\\overline{B P}$ bisects $\\angle E B N$. Perpendiculars $\\overline{P R}$ and $\\overline{P S}$ are drawn to sides $\\overline{B E}$ and $\\overline{B N}$, respectively. If $E N=221$ and $P R=60$, compute $\\frac{1}{B E}+\\frac{1}{B N}$.", "answer": "\\frac{1}{60}"}, {"index": 499, "question": "$\\quad$ Compute all real values of $x$ such that $\\log _{2}\\left(\\log _{2} x\\right)=\\log _{4}\\left(\\log _{4} x\\right)$.", "answer": "\\sqrt{2}"}, {"index": 500, "question": "Let $k$ be the least common multiple of the numbers in the set $\\mathcal{S}=\\{1,2, \\ldots, 30\\}$. Determine the number of positive integer divisors of $k$ that are divisible by exactly 28 of the numbers in the set $\\mathcal{S}$.", "answer": "23"}, {"index": 501, "question": "Let $A$ and $B$ be digits from the set $\\{0,1,2, \\ldots, 9\\}$. Let $r$ be the two-digit integer $\\underline{A} \\underline{B}$ and let $s$ be the two-digit integer $\\underline{B} \\underline{A}$, so that $r$ and $s$ are members of the set $\\{00,01, \\ldots, 99\\}$. Compute the number of ordered pairs $(A, B)$ such that $|r-s|=k^{2}$ for some integer $k$.", "answer": "42"}, {"index": 502, "question": "For $k \\geq 3$, we define an ordered $k$-tuple of real numbers $\\left(x_{1}, x_{2}, \\ldots, x_{k}\\right)$ to be special if, for every $i$ such that $1 \\leq i \\leq k$, the product $x_{1} \\cdot x_{2} \\cdot \\ldots \\cdot x_{k}=x_{i}^{2}$. Compute the smallest value of $k$ such that there are at least 2009 distinct special $k$-tuples.", "answer": "12"}, {"index": 503, "question": "A cylinder with radius $r$ and height $h$ has volume 1 and total surface area 12. Compute $\\frac{1}{r}+\\frac{1}{h}$.", "answer": "6"}, {"index": 504, "question": "If $6 \\tan ^{-1} x+4 \\tan ^{-1}(3 x)=\\pi$, compute $x^{2}$.", "answer": "\\frac{15-8\\sqrt{3}}{33}"}, {"index": 505, "question": "A rectangular box has dimensions $8 \\times 10 \\times 12$. Compute the fraction of the box's volume that is not within 1 unit of any of the box's faces.", "answer": "\\frac{1}{2}"}, {"index": 506, "question": "Let $T=T N Y W R$. Compute the largest real solution $x$ to $(\\log x)^{2}-\\log \\sqrt{x}=T$.", "answer": "10"}, {"index": 507, "question": "Let $T=T N Y W R$. <PERSON> has $T+1$ different colors of fingernail polish. Compute the number of ways that <PERSON> can paint the five fingernails on her left hand by using at least three colors and such that no two consecutive fingernails have the same color.", "answer": "109890"}, {"index": 508, "question": "Compute the number of ordered pairs $(x, y)$ of positive integers satisfying $x^{2}-8 x+y^{2}+4 y=5$.", "answer": "4"}, {"index": 509, "question": "Let $T=T N Y W R$ and let $k=21+2 T$. Compute the largest integer $n$ such that $2 n^{2}-k n+77$ is a positive prime number.", "answer": "12"}, {"index": 510, "question": "Let $T=T N Y W R$. In triangle $A B C, B C=T$ and $\\mathrm{m} \\angle B=30^{\\circ}$. Compute the number of integer values of $A C$ for which there are two possible values for side length $A B$.", "answer": "5"}, {"index": 511, "question": "An $\\boldsymbol{n}$-label is a permutation of the numbers 1 through $n$. For example, $J=35214$ is a 5 -label and $K=132$ is a 3 -label. For a fixed positive integer $p$, where $p \\leq n$, consider consecutive blocks of $p$ numbers in an $n$-label. For example, when $p=3$ and $L=263415$, the blocks are 263,634,341, and 415. We can associate to each of these blocks a $p$-label that corresponds to the relative order of the numbers in that block. For $L=263415$, we get the following:\n\n$$\n\\underline{263} 415 \\rightarrow 132 ; \\quad 2 \\underline{63415} \\rightarrow 312 ; \\quad 26 \\underline{341} 5 \\rightarrow 231 ; \\quad 263 \\underline{415} \\rightarrow 213\n$$\n\nMoving from left to right in the $n$-label, there are $n-p+1$ such blocks, which means we obtain an $(n-p+1)$-tuple of $p$-labels. For $L=263415$, we get the 4 -tuple $(132,312,231,213)$. We will call this $(n-p+1)$-tuple the $\\boldsymbol{p}$-signature of $L$ (or signature, if $p$ is clear from the context) and denote it by $S_{p}[L]$; the $p$-labels in the signature are called windows. For $L=263415$, the windows are $132,312,231$, and 213 , and we write\n\n$$\nS_{3}[263415]=(132,312,231,213)\n$$\n\nMore generally, we will call any $(n-p+1)$-tuple of $p$-labels a $p$-signature, even if we do not know of an $n$-label to which it corresponds (and even if no such label exists). A signature that occurs for exactly one $n$-label is called unique, and a signature that doesn't occur for any $n$-labels is called impossible. A possible signature is one that occurs for at least one $n$-label.\n\nIn this power question, you will be asked to analyze some of the properties of labels and signatures.\nCompute the 3 -signature for 52341.", "answer": "(312,123,231)"}, {"index": 512, "question": "An $\\boldsymbol{n}$-label is a permutation of the numbers 1 through $n$. For example, $J=35214$ is a 5 -label and $K=132$ is a 3 -label. For a fixed positive integer $p$, where $p \\leq n$, consider consecutive blocks of $p$ numbers in an $n$-label. For example, when $p=3$ and $L=263415$, the blocks are 263,634,341, and 415. We can associate to each of these blocks a $p$-label that corresponds to the relative order of the numbers in that block. For $L=263415$, we get the following:\n\n$$\n\\underline{263} 415 \\rightarrow 132 ; \\quad 2 \\underline{63415} \\rightarrow 312 ; \\quad 26 \\underline{341} 5 \\rightarrow 231 ; \\quad 263 \\underline{415} \\rightarrow 213\n$$\n\nMoving from left to right in the $n$-label, there are $n-p+1$ such blocks, which means we obtain an $(n-p+1)$-tuple of $p$-labels. For $L=263415$, we get the 4 -tuple $(132,312,231,213)$. We will call this $(n-p+1)$-tuple the $\\boldsymbol{p}$-signature of $L$ (or signature, if $p$ is clear from the context) and denote it by $S_{p}[L]$; the $p$-labels in the signature are called windows. For $L=263415$, the windows are $132,312,231$, and 213 , and we write\n\n$$\nS_{3}[263415]=(132,312,231,213)\n$$\n\nMore generally, we will call any $(n-p+1)$-tuple of $p$-labels a $p$-signature, even if we do not know of an $n$-label to which it corresponds (and even if no such label exists). A signature that occurs for exactly one $n$-label is called unique, and a signature that doesn't occur for any $n$-labels is called impossible. A possible signature is one that occurs for at least one $n$-label.\n\nIn this power question, you will be asked to analyze some of the properties of labels and signatures.\nFind another 5-label with the same 3-signature as in part (a).", "answer": "41352,42351,51342"}, {"index": 513, "question": "An $\\boldsymbol{n}$-label is a permutation of the numbers 1 through $n$. For example, $J=35214$ is a 5 -label and $K=132$ is a 3 -label. For a fixed positive integer $p$, where $p \\leq n$, consider consecutive blocks of $p$ numbers in an $n$-label. For example, when $p=3$ and $L=263415$, the blocks are 263,634,341, and 415. We can associate to each of these blocks a $p$-label that corresponds to the relative order of the numbers in that block. For $L=263415$, we get the following:\n\n$$\n\\underline{263} 415 \\rightarrow 132 ; \\quad 2 \\underline{63415} \\rightarrow 312 ; \\quad 26 \\underline{341} 5 \\rightarrow 231 ; \\quad 263 \\underline{415} \\rightarrow 213\n$$\n\nMoving from left to right in the $n$-label, there are $n-p+1$ such blocks, which means we obtain an $(n-p+1)$-tuple of $p$-labels. For $L=263415$, we get the 4 -tuple $(132,312,231,213)$. We will call this $(n-p+1)$-tuple the $\\boldsymbol{p}$-signature of $L$ (or signature, if $p$ is clear from the context) and denote it by $S_{p}[L]$; the $p$-labels in the signature are called windows. For $L=263415$, the windows are $132,312,231$, and 213 , and we write\n\n$$\nS_{3}[263415]=(132,312,231,213)\n$$\n\nMore generally, we will call any $(n-p+1)$-tuple of $p$-labels a $p$-signature, even if we do not know of an $n$-label to which it corresponds (and even if no such label exists). A signature that occurs for exactly one $n$-label is called unique, and a signature that doesn't occur for any $n$-labels is called impossible. A possible signature is one that occurs for at least one $n$-label.\n\nIn this power question, you will be asked to analyze some of the properties of labels and signatures.\nCompute two other 6-labels with the same 4-signature as 462135.", "answer": "352146,362145,452136,562134"}, {"index": 514, "question": "In $\\triangle A B C, D$ is on $\\overline{A C}$ so that $\\overline{B D}$ is the angle bisector of $\\angle B$. Point $E$ is on $\\overline{A B}$ and $\\overline{C E}$ intersects $\\overline{B D}$ at $P$. Quadrilateral $B C D E$ is cyclic, $B P=12$ and $P E=4$. Compute the ratio $\\frac{A C}{A E}$.", "answer": "3"}, {"index": 515, "question": "Let $N$ be a six-digit number formed by an arrangement of the digits $1,2,3,3,4,5$. Compute the smallest value of $N$ that is divisible by 264 .", "answer": "135432"}, {"index": 516, "question": "In triangle $A B C, A B=4, B C=6$, and $A C=8$. Squares $A B Q R$ and $B C S T$ are drawn external to and lie in the same plane as $\\triangle A B C$. Compute $Q T$.", "answer": "2\\sqrt{10}"}, {"index": 517, "question": "An ellipse in the first quadrant is tangent to both the $x$-axis and $y$-axis. One focus is at $(3,7)$, and the other focus is at $(d, 7)$. Compute $d$.", "answer": "\\frac{49}{3}"}, {"index": 518, "question": "Let $A_{1} A_{2} A_{3} A_{4} A_{5} A_{6} A_{7} A_{8}$ be a regular octagon. Let $\\mathbf{u}$ be the vector from $A_{1}$ to $A_{2}$ and let $\\mathbf{v}$ be the vector from $A_{1}$ to $A_{8}$. The vector from $A_{1}$ to $A_{4}$ can be written as $a \\mathbf{u}+b \\mathbf{v}$ for a unique ordered pair of real numbers $(a, b)$. Compute $(a, b)$.", "answer": "\\quad(2+\\sqrt{2},1+\\sqrt{2})"}, {"index": 519, "question": "Compute the integer $n$ such that $2009<n<3009$ and the sum of the odd positive divisors of $n$ is 1024 .", "answer": "2604"}, {"index": 520, "question": "Points $A, R, M$, and $L$ are consecutively the midpoints of the sides of a square whose area is 650. The coordinates of point $A$ are $(11,5)$. If points $R, M$, and $L$ are all lattice points, and $R$ is in Quadrant I, compute the number of possible ordered pairs $(x, y)$ of coordinates for point $R$.", "answer": "10"}, {"index": 521, "question": "The taxicab distance between points $\\left(x_{1}, y_{1}, z_{1}\\right)$ and $\\left(x_{2}, y_{2}, z_{2}\\right)$ is given by\n\n$$\nd\\left(\\left(x_{1}, y_{1}, z_{1}\\right),\\left(x_{2}, y_{2}, z_{2}\\right)\\right)=\\left|x_{1}-x_{2}\\right|+\\left|y_{1}-y_{2}\\right|+\\left|z_{1}-z_{2}\\right| .\n$$\n\nThe region $\\mathcal{R}$ is obtained by taking the cube $\\{(x, y, z): 0 \\leq x, y, z \\leq 1\\}$ and removing every point whose taxicab distance to any vertex of the cube is less than $\\frac{3}{5}$. Compute the volume of $\\mathcal{R}$.", "answer": "\\frac{179}{250}"}, {"index": 522, "question": "$\\quad$ Let $a$ and $b$ be real numbers such that\n\n$$\na^{3}-15 a^{2}+20 a-50=0 \\quad \\text { and } \\quad 8 b^{3}-60 b^{2}-290 b+2575=0\n$$\n\nCompute $a+b$.", "answer": "\\frac{15}{2}"}, {"index": 523, "question": "For a positive integer $n$, define $s(n)$ to be the sum of $n$ and its digits. For example, $s(2009)=2009+2+0+0+9=2020$. Compute the number of elements in the set $\\{s(0), s(1), s(2), \\ldots, s(9999)\\}$.", "answer": "9046"}, {"index": 524, "question": "Quadrilateral $A R M L$ is a kite with $A R=R M=5, A M=8$, and $R L=11$. Compute $A L$.", "answer": "4\\sqrt{5}"}, {"index": 525, "question": "Let $T=4 \\sqrt{5}$. If $x y=\\sqrt{5}, y z=5$, and $x z=T$, compute the positive value of $x$.", "answer": "2"}, {"index": 526, "question": "$\\quad$ Let $T=2$. In how many ways can $T$ boys and $T+1$ girls be arranged in a row if all the girls must be standing next to each other?", "answer": "36"}, {"index": 527, "question": "$\\triangle A B C$ is on a coordinate plane such that $A=(3,6)$, $B=(T, 0)$, and $C=(2 T-1,1-T)$. Let $\\ell$ be the line containing the altitude to $\\overline{B C}$. Compute the $y$-intercept of $\\ell$.", "answer": "3"}, {"index": 528, "question": "Let $T=3$. In triangle $A B C, A B=A C-2=T$, and $\\mathrm{m} \\angle A=60^{\\circ}$. Compute $B C^{2}$.", "answer": "19"}, {"index": 529, "question": "Let $T=19$. Let $\\mathcal{S}_{1}$ denote the arithmetic sequence $0, \\frac{1}{4}, \\frac{1}{2}, \\ldots$, and let $\\mathcal{S}_{2}$ denote the arithmetic sequence $0, \\frac{1}{6}, \\frac{1}{3}, \\ldots$ Compute the $T^{\\text {th }}$ smallest number that occurs in both sequences $\\mathcal{S}_{1}$ and $\\mathcal{S}_{2}$.", "answer": "9"}, {"index": 530, "question": "$\\quad$ Let $T=9$. An integer $n$ is randomly selected from the set $\\{1,2,3, \\ldots, 2 T\\}$. Compute the probability that the integer $\\left|n^{3}-7 n^{2}+13 n-6\\right|$ is a prime number.", "answer": "\\frac{1}{9}"}, {"index": 531, "question": "Let $A=\\frac{1}{9}$, and let $B=\\frac{1}{25}$. In $\\frac{1}{A}$ minutes, 20 frogs can eat 1800 flies. At this rate, in $\\frac{1}{B}$ minutes, how many flies will 15 frogs be able to eat?", "answer": "3750"}, {"index": 532, "question": "Let $T=5$. If $|T|-1+3 i=\\frac{1}{z}$, compute the sum of the real and imaginary parts of $z$.", "answer": "\\frac{1}{25}"}, {"index": 533, "question": "Let $T=10$. <PERSON> spends 80 seconds climbing up a $T$ meter rope at a constant speed, and she spends 70 seconds climbing down the same rope at a constant speed (different from her upward speed). <PERSON> begins climbing up and down the rope repeatedly, and she does not pause after climbing the length of the rope. After $T$ minutes, how many meters will <PERSON> have climbed in either direction?", "answer": "80"}, {"index": 534, "question": "Let $T=800$. Simplify $2^{\\log _{4} T} / 2^{\\log _{16} 64}$.", "answer": "10"}, {"index": 535, "question": "Let $P(x)=x^{2}+T x+800$, and let $r_{1}$ and $r_{2}$ be the roots of $P(x)$. The polynomial $Q(x)$ is quadratic, it has leading coefficient 1, and it has roots $r_{1}+1$ and $r_{2}+1$. Find the sum of the coefficients of $Q(x)$.", "answer": "800"}, {"index": 536, "question": "Let $T=12$. Equilateral triangle $A B C$ is given with side length $T$. Points $D$ and $E$ are the midpoints of $\\overline{A B}$ and $\\overline{A C}$, respectively. Point $F$ lies in space such that $\\triangle D E F$ is equilateral and $\\triangle D E F$ lies in a plane perpendicular to the plane containing $\\triangle A B C$. Compute the volume of tetrahedron $A B C F$.", "answer": "108"}, {"index": 537, "question": "In triangle $A B C, A B=5, A C=6$, and $\\tan \\angle B A C=-\\frac{4}{3}$. Compute the area of $\\triangle A B C$.", "answer": "12"}, {"index": 538, "question": "Compute the number of positive integers less than 25 that cannot be written as the difference of two squares of integers.", "answer": "6"}, {"index": 539, "question": "For digits $A, B$, and $C,(\\underline{A} \\underline{B})^{2}+(\\underline{A} \\underline{C})^{2}=1313$. Compute $A+B+C$.", "answer": "13"}, {"index": 540, "question": "Points $P, Q, R$, and $S$ lie in the interior of square $A B C D$ such that triangles $A B P, B C Q$, $C D R$, and $D A S$ are equilateral. If $A B=1$, compute the area of quadrilateral $P Q R S$.", "answer": "2-\\sqrt{3}"}, {"index": 541, "question": "For real numbers $\\alpha, B$, and $C$, the zeros of $T(x)=x^{3}+x^{2}+B x+C \\operatorname{are~}^{2} \\alpha$, $\\cos ^{2} \\alpha$, and $-\\csc ^{2} \\alpha$. Compute $T(5)$.", "answer": "\\frac{567}{4}"}, {"index": 542, "question": "Let $\\mathcal{R}$ denote the circular region bounded by $x^{2}+y^{2}=36$. The lines $x=4$ and $y=3$ partition $\\mathcal{R}$ into four regions $\\mathcal{R}_{1}, \\mathcal{R}_{2}, \\mathcal{R}_{3}$, and $\\mathcal{R}_{4}$. $\\left[\\mathcal{R}_{i}\\right]$ denotes the area of region $\\mathcal{R}_{i}$. If $\\left[\\mathcal{R}_{1}\\right]>\\left[\\mathcal{R}_{2}\\right]>\\left[\\mathcal{R}_{3}\\right]>\\left[\\mathcal{R}_{4}\\right]$, compute $\\left[\\mathcal{R}_{1}\\right]-\\left[\\mathcal{R}_{2}\\right]-\\left[\\mathcal{R}_{3}\\right]+\\left[\\mathcal{R}_{4}\\right]$.", "answer": "48"}, {"index": 543, "question": "Let $x$ be a real number in the interval $[0,360]$ such that the four expressions $\\sin x^{\\circ}, \\cos x^{\\circ}$, $\\tan x^{\\circ}, \\cot x^{\\circ}$ take on exactly three distinct (finite) real values. Compute the sum of all possible values of $x$.", "answer": "990"}, {"index": 544, "question": "Let $a_{1}, a_{2}, a_{3}, \\ldots$ be an arithmetic sequence, and let $b_{1}, b_{2}, b_{3}, \\ldots$ be a geometric sequence. The sequence $c_{1}, c_{2}, c_{3}, \\ldots$ has $c_{n}=a_{n}+b_{n}$ for each positive integer $n$. If $c_{1}=1, c_{2}=4, c_{3}=15$, and $c_{4}=2$, compute $c_{5}$.", "answer": "61"}, {"index": 545, "question": "In square $A B C D$ with diagonal $1, E$ is on $\\overline{A B}$ and $F$ is on $\\overline{B C}$ with $\\mathrm{m} \\angle B C E=\\mathrm{m} \\angle B A F=$ $30^{\\circ}$. If $\\overline{C E}$ and $\\overline{A F}$ intersect at $G$, compute the distance between the incenters of triangles $A G E$ and $C G F$.", "answer": "4-2\\sqrt{3}"}, {"index": 546, "question": "Let $a, b, m, n$ be positive integers with $a m=b n=120$ and $a \\neq b$. In the coordinate plane, let $A=(a, m), B=(b, n)$, and $O=(0,0)$. If $X$ is a point in the plane such that $A O B X$ is a parallelogram, compute the minimum area of $A O B X$.", "answer": "44"}, {"index": 547, "question": "Let $\\mathcal{S}$ be the set of integers from 0 to 9999 inclusive whose base- 2 and base- 5 representations end in the same four digits. (Leading zeros are allowed, so $1=0001_{2}=0001_{5}$ is one such number.) Compute the remainder when the sum of the elements of $\\mathcal{S}$ is divided by 10,000.", "answer": "6248"}, {"index": 548, "question": "If $A, R, M$, and $L$ are positive integers such that $A^{2}+R^{2}=20$ and $M^{2}+L^{2}=10$, compute the product $A \\cdot R \\cdot M \\cdot L$.", "answer": "24"}, {"index": 549, "question": "Let $T=49$. Compute the last digit, in base 10, of the sum\n\n$$\nT^{2}+(2 T)^{2}+(3 T)^{2}+\\ldots+\\left(T^{2}\\right)^{2}\n$$", "answer": "5"}, {"index": 550, "question": "A fair coin is flipped $n$ times. Compute the smallest positive integer $n$ for which the probability that the coin has the same result every time is less than $10 \\%$.", "answer": "5"}, {"index": 551, "question": "Let $T=5$. Compute the smallest positive integer $n$ such that there are at least $T$ positive integers in the domain of $f(x)=\\sqrt{-x^{2}-2 x+n}$.", "answer": "35"}, {"index": 552, "question": "Let $T=35$. Compute the smallest positive real number $x$ such that $\\frac{\\lfloor x\\rfloor}{x-\\lfloor x\\rfloor}=T$.", "answer": "\\frac{36}{35}"}, {"index": 553, "question": "Let set $S=\\{1,2,3,4,5,6\\}$, and let set $T$ be the set of all subsets of $S$ (including the empty set and $S$ itself). Let $t_{1}, t_{2}, t_{3}$ be elements of $T$, not necessarily distinct. The ordered triple $\\left(t_{1}, t_{2}, t_{3}\\right)$ is called satisfactory if either\n\n(a) both $t_{1} \\subseteq t_{3}$ and $t_{2} \\subseteq t_{3}$, or\n\n(b) $t_{3} \\subseteq t_{1}$ and $t_{3} \\subseteq t_{2}$.\n\nCompute the number of satisfactory ordered triples $\\left(t_{1}, t_{2}, t_{3}\\right)$.", "answer": "31186"}, {"index": 554, "question": "Let $A B C D$ be a parallelogram with $\\angle A B C$ obtuse. Let $\\overline{B E}$ be the altitude to side $\\overline{A D}$ of $\\triangle A B D$. Let $X$ be the point of intersection of $\\overline{A C}$ and $\\overline{B E}$, and let $F$ be the point of intersection of $\\overline{A B}$ and $\\overleftrightarrow{D X}$. If $B C=30, C D=13$, and $B E=12$, compute the ratio $\\frac{A C}{A F}$.", "answer": "\\frac{222}{13}"}, {"index": 555, "question": "Compute the sum of all positive two-digit factors of $2^{32}-1$.", "answer": "168"}, {"index": 556, "question": "Compute all ordered pairs of real numbers $(x, y)$ that satisfy both of the equations:\n\n$$\nx^{2}+y^{2}=6 y-4 x+12 \\quad \\text { and } \\quad 4 y=x^{2}+4 x+12\n$$", "answer": "(-6,6),(2,6)"}, {"index": 557, "question": "Define $\\log ^{*}(n)$ to be the smallest number of times the log function must be iteratively applied to $n$ to get a result less than or equal to 1 . For example, $\\log ^{*}(1000)=2$ since $\\log 1000=3$ and $\\log (\\log 1000)=\\log 3=0.477 \\ldots \\leq 1$. Let $a$ be the smallest integer such that $\\log ^{*}(a)=3$. Compute the number of zeros in the base 10 representation of $a$.", "answer": "9"}, {"index": 558, "question": "An integer $N$ is worth 1 point for each pair of digits it contains that forms a prime in its original order. For example, 6733 is worth 3 points (for 67,73 , and 73 again), and 20304 is worth 2 points (for 23 and 03). Compute the smallest positive integer that is worth exactly 11 points. [Note: Leading zeros are not allowed in the original integer.]", "answer": "100337"}, {"index": 559, "question": "The six sides of convex hexagon $A_{1} A_{2} A_{3} A_{4} A_{5} A_{6}$ are colored red. Each of the diagonals of the hexagon is colored either red or blue. Compute the number of colorings such that every triangle $A_{i} A_{j} A_{k}$ has at least one red side.", "answer": "392"}, {"index": 560, "question": "Compute the smallest positive integer $n$ such that $n^{n}$ has at least 1,000,000 positive divisors.", "answer": "84"}, {"index": 561, "question": "Given an arbitrary finite sequence of letters (represented as a word), a subsequence is a sequence of one or more letters that appear in the same order as in the original sequence. For example, $N, C T, O T T$, and CONTEST are subsequences of the word CONTEST, but NOT, ONSET, and TESS are not. Assuming the standard English alphabet $\\{A, B, \\ldots, Z\\}$, compute the number of distinct four-letter \"words\" for which $E E$ is a subsequence.", "answer": "3851"}, {"index": 562, "question": "Six solid regular tetrahedra are placed on a flat surface so that their bases form a regular hexagon $\\mathcal{H}$ with side length 1 , and so that the vertices not lying in the plane of $\\mathcal{H}$ (the \"top\" vertices) are themselves coplanar. A spherical ball of radius $r$ is placed so that its center is directly above the center of the hexagon. The sphere rests on the tetrahedra so that it is tangent to one edge from each tetrahedron. If the ball's center is coplanar with the top vertices of the tetrahedra, compute $r$.", "answer": "\\frac{\\sqrt{2}}{3}"}, {"index": 563, "question": "<PERSON> starts at the point $(0,0)$, facing the point $(0,1)$, and he wants to get to the point $(1,1)$. He takes unit steps parallel to the coordinate axes. A move consists of either a step forward, or a $90^{\\circ}$ right (clockwise) turn followed by a step forward, so that his path does not contain any left turns. His path is restricted to the square region defined by $0 \\leq x \\leq 17$ and $0 \\leq y \\leq 17$. Compute the number of ways he can get to $(1,1)$ without returning to any previously visited point.", "answer": "529"}, {"index": 564, "question": "The equations $x^{3}+A x+10=0$ and $x^{3}+B x^{2}+50=0$ have two roots in common. Compute the product of these common roots.", "answer": "5\\sqrt[3]{4}"}, {"index": 565, "question": "Let $N$ be a perfect square between 100 and 400 , inclusive. What is the only digit that cannot appear in $N$ ?", "answer": "7"}, {"index": 566, "question": "Let $T=7$. Let $A$ and $B$ be distinct digits in base $T$, and let $N$ be the largest number of the form $\\underline{A} \\underline{B} \\underline{A}_{T}$. Compute the value of $N$ in base 10 .", "answer": "335"}, {"index": 567, "question": "Let T be an integer. Given a nonzero integer $n$, let $f(n)$ denote the sum of all numbers of the form $i^{d}$, where $i=\\sqrt{-1}$, and $d$ is a divisor (positive or negative) of $n$. Compute $f(2 T+1)$.", "answer": "0"}, {"index": 568, "question": "Let $T=0$. Compute the real value of $x$ for which there exists a solution to the system of equations\n\n$$\n\\begin{aligned}\nx+y & =0 \\\\\nx^{3}-y^{3} & =54+T .\n\\end{aligned}\n$$", "answer": "3"}, {"index": 569, "question": "Let $T=3$. In $\\triangle A B C, A C=T^{2}, \\mathrm{~m} \\angle A B C=45^{\\circ}$, and $\\sin \\angle A C B=\\frac{8}{9}$. Compute $A B$.", "answer": "8\\sqrt{2}"}, {"index": 570, "question": "Let $T=9$. The sequence $a_{1}, a_{2}, a_{3}, \\ldots$ is an arithmetic progression, $d$ is the common difference, $a_{T}=10$, and $a_{K}=2010$, where $K>T$. If $d$ is an integer, compute the value of $K$ such that $|K-d|$ is minimal.", "answer": "49"}, {"index": 571, "question": "Let $A$ be the number you will receive from position 7 , and let $B$ be the number you will receive from position 9 . There are exactly two ordered pairs of real numbers $\\left(x_{1}, y_{1}\\right),\\left(x_{2}, y_{2}\\right)$ that satisfy both $|x+y|=6(\\sqrt{A}-5)$ and $x^{2}+y^{2}=B^{2}$. Compute $\\left|x_{1}\\right|+\\left|y_{1}\\right|+\\left|x_{2}\\right|+\\left|y_{2}\\right|$.", "answer": "24"}, {"index": 572, "question": "Let $T=23$. In triangle $A B C$, the altitude from $A$ to $\\overline{B C}$ has length $\\sqrt{T}, A B=A C$, and $B C=T-K$, where $K$ is the real root of the equation $x^{3}-8 x^{2}-8 x-9=0$. Compute the length $A B$.", "answer": "6\\sqrt{2}"}, {"index": 573, "question": "Let $T=8$. A cube has volume $T-2$. The cube's surface area equals one-eighth the surface area of a $2 \\times 2 \\times n$ rectangular prism. Compute $n$.", "answer": "23"}, {"index": 574, "question": "Let $T=98721$, and let $K$ be the sum of the digits of $T$. Let $A_{n}$ be the number of ways to tile a $1 \\times n$ rectangle using $1 \\times 3$ and $1 \\times 1$ tiles that do not overlap. Tiles of both types need not be used; for example, $A_{3}=2$ because a $1 \\times 3$ rectangle can be tiled with three $1 \\times 1$ tiles or one $1 \\times 3$ tile. Compute the smallest value of $n$ such that $A_{n} \\geq K$.", "answer": "10"}, {"index": 575, "question": "Let $T=3$, and let $K=T+2$. Compute the largest $K$-digit number which has distinct digits and is a multiple of 63.", "answer": "98721"}, {"index": 576, "question": "Let $T\\neq 0$. Suppose that $a, b, c$, and $d$ are real numbers so that $\\log _{a} c=\\log _{b} d=T$. Compute\n\n$$\n\\frac{\\log _{\\sqrt{a b}}(c d)^{3}}{\\log _{a} c+\\log _{b} d}\n$$", "answer": "3"}, {"index": 577, "question": "Let $T=2030$. Given that $\\mathrm{A}, \\mathrm{D}, \\mathrm{E}, \\mathrm{H}, \\mathrm{S}$, and $\\mathrm{W}$ are distinct digits, and that $\\underline{\\mathrm{W}} \\underline{\\mathrm{A}} \\underline{\\mathrm{D}} \\underline{\\mathrm{E}}+\\underline{\\mathrm{A}} \\underline{\\mathrm{S}} \\underline{\\mathrm{H}}=T$, what is the largest possible value of $\\mathrm{D}+\\mathrm{E}$ ?", "answer": "9"}, {"index": 578, "question": "Let $f(x)=2^{x}+x^{2}$. Compute the smallest integer $n>10$ such that $f(n)$ and $f(10)$ have the same units digit.", "answer": "30"}, {"index": 579, "question": "In rectangle $P A U L$, point $D$ is the midpoint of $\\overline{U L}$ and points $E$ and $F$ lie on $\\overline{P L}$ and $\\overline{P A}$, respectively such that $\\frac{P E}{E L}=\\frac{3}{2}$ and $\\frac{P F}{F A}=2$. Given that $P A=36$ and $P L=25$, compute the area of pentagon $A U D E F$.", "answer": "630"}, {"index": 580, "question": "Rectangle $A R M L$ has length 125 and width 8. The rectangle is divided into 1000 squares of area 1 by drawing in gridlines parallel to the sides of $A R M L$. Diagonal $\\overline{A M}$ passes through the interior of exactly $n$ of the 1000 unit squares. Compute $n$.", "answer": "132"}, {"index": 581, "question": "Compute the least integer $n>1$ such that the product of all positive divisors of $n$ equals $n^{4}$.", "answer": "24"}, {"index": 582, "question": "Each of the six faces of a cube is randomly colored red or blue with equal probability. Compute the probability that no three faces of the same color share a common vertex.", "answer": "\\frac{9}{32}"}, {"index": 583, "question": "Scalene triangle $A B C$ has perimeter 2019 and integer side lengths. The angle bisector from $C$ meets $\\overline{A B}$ at $D$ such that $A D=229$. Given that $A C$ and $A D$ are relatively prime, compute $B C$.", "answer": "888"}, {"index": 584, "question": "Given that $a$ and $b$ are positive and\n\n$$\n\\lfloor 20-a\\rfloor=\\lfloor 19-b\\rfloor=\\lfloor a b\\rfloor,\n$$\n\ncompute the least upper bound of the set of possible values of $a+b$.", "answer": "\\frac{41}{5}"}, {"index": 585, "question": "Compute the number of five-digit integers $\\underline{M} \\underline{A} \\underline{R} \\underline{T} \\underline{Y}$, with all digits distinct, such that $M>A>R$ and $R<T<Y$.", "answer": "1512"}, {"index": 586, "question": "In parallelogram $A R M L$, points $P$ and $Q$ are the midpoints of sides $\\overline{R M}$ and $\\overline{A L}$, respectively. Point $X$ lies on segment $\\overline{P Q}$, and $P X=3, R X=4$, and $P R=5$. Point $I$ lies on segment $\\overline{R X}$ such that $I A=I L$. Compute the maximum possible value of $\\frac{[P Q R]}{[L I P]}$.", "answer": "\\frac{4}{3}"}, {"index": 587, "question": "Given that $a, b, c$, and $d$ are positive integers such that\n\n$$\na ! \\cdot b ! \\cdot c !=d ! \\quad \\text { and } \\quad a+b+c+d=37\n$$\n\ncompute the product $a b c d$.", "answer": "2240"}, {"index": 588, "question": "Compute the value of\n\n$$\n\\sin \\left(6^{\\circ}\\right) \\cdot \\sin \\left(12^{\\circ}\\right) \\cdot \\sin \\left(24^{\\circ}\\right) \\cdot \\sin \\left(42^{\\circ}\\right)+\\sin \\left(12^{\\circ}\\right) \\cdot \\sin \\left(24^{\\circ}\\right) \\cdot \\sin \\left(42^{\\circ}\\right) \\text {. }\n$$", "answer": "\\frac{1}{16}"}, {"index": 589, "question": "Let $a=19, b=20$, and $c=21$. Compute\n\n$$\n\\frac{a^{2}+b^{2}+c^{2}+2 a b+2 b c+2 c a}{a+b+c}\n$$", "answer": "60"}, {"index": 590, "question": "Let $T=60$ . <PERSON> is a professional swimmer and can swim one-fifth of a lap of a pool in an impressive 20.19 seconds, and she swims at a constant rate. Rounded to the nearest integer, compute the number of minutes required for <PERSON> to swim $T$ laps.", "answer": "101"}, {"index": 591, "question": "Let $T=101$. In $\\triangle A B C, \\mathrm{~m} \\angle C=90^{\\circ}$ and $A C=B C=\\sqrt{T-3}$. Circles $O$ and $P$ each have radius $r$ and lie inside $\\triangle A B C$. Circle $O$ is tangent to $\\overline{A C}$ and $\\overline{B C}$. Circle $P$ is externally tangent to circle $O$ and to $\\overline{A B}$. Given that points $C, O$, and $P$ are collinear, compute $r$.", "answer": "3-\\sqrt{2}"}, {"index": 592, "question": "Given that $p=6.6 \\times 10^{-27}$, then $\\sqrt{p}=a \\times 10^{b}$, where $1 \\leq a<10$ and $b$ is an integer. Compute $10 a+b$ rounded to the nearest integer.", "answer": "67"}, {"index": 593, "question": "Let $T=67$. A group of children and adults go to a rodeo. A child's admission ticket costs $\\$ 5$, and an adult's admission ticket costs more than $\\$ 5$. The total admission cost for the group is $\\$ 10 \\cdot T$. If the number of adults in the group were to increase by $20 \\%$, then the total cost would increase by $10 \\%$. Compute the number of children in the group.", "answer": "67"}, {"index": 594, "question": "Let $T=67$. Rectangles $F A K E$ and $F U N K$ lie in the same plane. Given that $E F=T$, $A F=\\frac{4 T}{3}$, and $U F=\\frac{12}{5}$, compute the area of the intersection of the two rectangles.", "answer": "262"}, {"index": 595, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nCompute the $E(6,1)$", "answer": "6"}, {"index": 596, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nCompute the $E(6,2)$", "answer": "3"}, {"index": 597, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nCompute the $E(7,3)$", "answer": "3"}, {"index": 598, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nCompute the $E(9,5)$", "answer": "3"}, {"index": 599, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nFind the following in terms of $n$. $E(n, 2)$ for positive even integers $n$", "answer": "\\frac{n}{2}"}, {"index": 600, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nFind the following in terms of $n$. $E(n, n-2)$ for $n \\geq 5$", "answer": "3"}, {"index": 601, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nFind the $E(2020,1993)$", "answer": "76"}, {"index": 602, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nFind the $E(2001,501)$", "answer": "5"}, {"index": 603, "question": "<PERSON> is in an \"escape room\" puzzle. She is in a room with one door which is locked at the start of the puzzle. The room contains $n$ light switches, each of which is initially off. Each minute, she must flip exactly $k$ different light switches (to \"flip\" a switch means to turn it on if it is currently off, and off if it is currently on). At the end of each minute, if all of the switches are on, then the door unlocks and <PERSON> escapes from the room.\n\nLet $E(n, k)$ be the minimum number of minutes required for <PERSON> to escape, for positive integers $n, k$ with $k \\leq n$. For example, $E(2,1)=2$ because <PERSON> cannot escape in one minute (there are two switches and one must be flipped every minute) but she can escape in two minutes (by flipping Switch 1 in the first minute and Switch 2 in the second minute). Define $E(n, k)=\\infty$ if the puzzle is impossible to solve (that is, if it is impossible to have all switches on at the end of any minute).\n\nFor convenience, assume the $n$ light switches are numbered 1 through $n$.\nOne might guess that in most cases, $E(n, k) \\approx \\frac{n}{k}$. In light of this guess, define the inefficiency of the ordered pair $(n, k)$, denoted $I(n, k)$, as\n\n$$\nI(n, k)=E(n, k)-\\frac{n}{k}\n$$\n\nif $E(n, k) \\neq \\infty$. If $E(n, k)=\\infty$, then by convention, $I(n, k)$ is undefined.\n\nCompute $I(6,3)$.", "answer": "0"}, {"index": 604, "question": "Regular tetrahedra $J A N E, J O H N$, and $J O A N$ have non-overlapping interiors. Compute $\\tan \\angle H A E$.", "answer": "\\frac{5\\sqrt{2}}{2}"}, {"index": 605, "question": "Each positive integer less than or equal to 2019 is written on a blank sheet of paper, and each of the digits 0 and 5 is erased. Compute the remainder when the product of the remaining digits on the sheet of paper is divided by 1000 .", "answer": "976"}, {"index": 606, "question": "Compute the third least positive integer $n$ such that each of $n, n+1$, and $n+2$ is a product of exactly two (not necessarily distinct) primes.", "answer": "93"}, {"index": 607, "question": "The points $(1,2,3)$ and $(3,3,2)$ are vertices of a cube. Compute the product of all possible distinct volumes of the cube.", "answer": "216"}, {"index": 608, "question": "Eight students attend a Harper Valley ARML practice. At the end of the practice, they decide to take selfies to celebrate the event. Each selfie will have either two or three students in the picture. Compute the minimum number of selfies so that each pair of the eight students appears in exactly one selfie.", "answer": "12"}, {"index": 609, "question": "$\\quad$ Compute the least positive value of $t$ such that\n\n$$\n\\operatorname{<PERSON><PERSON>}(\\sin (t)), \\operatorname{Arccos}(\\cos (t)), \\operatorname{Arc<PERSON>}(\\tan (t))\n$$\n\nform (in some order) a three-term arithmetic progression with a nonzero common difference.", "answer": "\\frac{3\\pi}{4}"}, {"index": 610, "question": "In non-right triangle $A B C$, distinct points $P, Q, R$, and $S$ lie on $\\overline{B C}$ in that order such that $\\angle B A P \\cong \\angle P A Q \\cong \\angle Q A R \\cong \\angle R A S \\cong \\angle S A C$. Given that the angles of $\\triangle A B C$ are congruent to the angles of $\\triangle A P Q$ in some order of correspondence, compute $\\mathrm{m} \\angle B$ in degrees.", "answer": "\\frac{45}{2}"}, {"index": 611, "question": "Consider the system of equations\n\n$$\n\\begin{aligned}\n& \\log _{4} x+\\log _{8}(y z)=2 \\\\\n& \\log _{4} y+\\log _{8}(x z)=4 \\\\\n& \\log _{4} z+\\log _{8}(x y)=5 .\n\\end{aligned}\n$$\n\nGiven that $x y z$ can be expressed in the form $2^{k}$, compute $k$.", "answer": "\\frac{66}{7}"}, {"index": 612, "question": "A complex number $z$ is selected uniformly at random such that $|z|=1$. Compute the probability that $z$ and $z^{2019}$ both lie in Quadrant II in the complex plane.", "answer": "\\frac{505}{8076}"}, {"index": 613, "question": "Compute the least positive integer $n$ such that the sum of the digits of $n$ is five times the sum of the digits of $(n+2019)$.", "answer": "7986"}, {"index": 614, "question": "$\\quad$ Compute the greatest real number $K$ for which the graphs of\n\n$$\n(|x|-5)^{2}+(|y|-5)^{2}=K \\quad \\text { and } \\quad(x-1)^{2}+(y+1)^{2}=37\n$$\n\nhave exactly two intersection points.", "answer": "29"}, {"index": 615, "question": "To morph a sequence means to replace two terms $a$ and $b$ with $a+1$ and $b-1$ if and only if $a+1<b-1$, and such an operation is referred to as a morph. Compute the least number of morphs needed to transform the sequence $1^{2}, 2^{2}, 3^{2}, \\ldots, 10^{2}$ into an arithmetic progression.", "answer": "56"}, {"index": 616, "question": "Triangle $A B C$ is inscribed in circle $\\omega$. The tangents to $\\omega$ at $B$ and $C$ meet at point $T$. The tangent to $\\omega$ at $A$ intersects the perpendicular bisector of $\\overline{A T}$ at point $P$. Given that $A B=14, A C=30$, and $B C=40$, compute $[P B C]$.", "answer": "\\frac{800}{3}"}, {"index": 617, "question": "Given that $a, b, c$, and $d$ are integers such that $a+b c=20$ and $-a+c d=19$, compute the greatest possible value of $c$.", "answer": "39"}, {"index": 618, "question": "Let $T$ = 39. <PERSON><PERSON> randomly chooses a set of $T$ cards from a standard deck of 52 cards. Given that <PERSON><PERSON>'s set contains no clubs, compute the probability that his set contains three aces.", "answer": "1"}, {"index": 619, "question": "Let $T=1$. In parallelogram $A B C D, \\frac{A B}{B C}=T$. Given that $M$ is the midpoint of $\\overline{A B}$ and $P$ and $Q$ are the trisection points of $\\overline{C D}$, compute $\\frac{[A B C D]}{[M P Q]}$.", "answer": "6"}, {"index": 620, "question": "Let $T=6$. Compute the value of $x$ such that $\\log _{T} \\sqrt{x-7}+\\log _{T^{2}}(x-2)=1$.", "answer": "11"}, {"index": 621, "question": "Let $T=11$. Let $p$ be an odd prime and let $x, y$, and $z$ be positive integers less than $p$. When the trinomial $(p x+y+z)^{T-1}$ is expanded and simplified, there are $N$ terms, of which $M$ are always multiples of $p$. Compute $M$.", "answer": "55"}, {"index": 622, "question": "Let $T=55$. Compute the value of $K$ such that $20, T-5, K$ is an increasing geometric sequence and $19, K, 4 T+11$ is an increasing arithmetic sequence.", "answer": "125"}, {"index": 623, "question": "Let $T=125$. Cube $\\mathcal{C}_{1}$ has volume $T$ and sphere $\\mathcal{S}_{1}$ is circumscribed about $\\mathcal{C}_{1}$. For $n \\geq 1$, the sphere $\\mathcal{S}_{n}$ is circumscribed about the cube $\\mathcal{C}_{n}$ and is inscribed in the cube $\\mathcal{C}_{n+1}$. Let $k$ be the least integer such that the volume of $\\mathcal{C}_{k}$ is at least 2019. Compute the edge length of $\\mathcal{C}_{k}$.", "answer": "15"}, {"index": 624, "question": "Square $K E N T$ has side length 20 . Point $M$ lies in the interior of $K E N T$ such that $\\triangle M E N$ is equilateral. Given that $K M^{2}=a-b \\sqrt{3}$, where $a$ and $b$ are integers, compute $b$.", "answer": "400"}, {"index": 625, "question": "Let $T$ be a rational number. Let $a, b$, and $c$ be the three solutions of the equation $x^{3}-20 x^{2}+19 x+T=0$. Compute $a^{2}+b^{2}+c^{2}$.", "answer": "362"}, {"index": 626, "question": "Let $T=362$ and let $K=\\sqrt{T-1}$. Compute $\\left|(K-20)(K+1)+19 K-K^{2}\\right|$.", "answer": "20"}, {"index": 627, "question": "Let $T=20$. In $\\triangle L E O, \\sin \\angle L E O=\\frac{1}{T}$. If $L E=\\frac{1}{n}$ for some positive real number $n$, then $E O=$ $n^{3}-4 n^{2}+5 n$. As $n$ ranges over the positive reals, compute the least possible value of $[L E O]$.", "answer": "\\frac{1}{40}"}, {"index": 628, "question": "Let $T=\\frac{1}{40}$. Given that $x, y$, and $z$ are real numbers such that $x+y=5, x^{2}-y^{2}=\\frac{1}{T}$, and $x-z=-7$, compute $x+z$", "answer": "20"}, {"index": 629, "question": "Let $T=20$. The product of all positive divisors of $2^{T}$ can be written in the form $2^{K}$. Compute $K$.", "answer": "210"}, {"index": 630, "question": "Let $T=210$. At the Westward House of Supper (\"WHS\"), a dinner special consists of an appetizer, an entrée, and dessert. There are 7 different appetizers and $K$ different entrées that a guest could order. There are 2 dessert choices, but ordering dessert is optional. Given that there are $T$ possible different orders that could be placed at the WHS, compute $K$.", "answer": "10"}, {"index": 631, "question": "Let $S=15$ and let $M=10$ . <PERSON> and <PERSON> each ride a bicycle at a constant speed. <PERSON>'s speed is $S \\mathrm{~km} / \\mathrm{hr}$ and <PERSON>'s speed is $M \\mathrm{~km} / \\mathrm{hr}$. Given that <PERSON> and <PERSON> are initially $100 \\mathrm{~km}$ apart and they begin riding towards one another at the same time, along a straight path, compute the number of kilometers that <PERSON> will have traveled when <PERSON> and <PERSON> meet.", "answer": "60"}, {"index": 632, "question": "Compute the $2011^{\\text {th }}$ smallest positive integer $N$ that gains an extra digit when doubled.", "answer": "6455"}, {"index": 633, "question": "In triangle $A B C, C$ is a right angle and $M$ is on $\\overline{A C}$. A circle with radius $r$ is centered at $M$, is tangent to $\\overline{A B}$, and is tangent to $\\overline{B C}$ at $C$. If $A C=5$ and $B C=12$, compute $r$.", "answer": "\\frac{12}{5}"}, {"index": 634, "question": "The product of the first five terms of a geometric progression is 32 . If the fourth term is 17 , compute the second term.", "answer": "\\frac{4}{17}"}, {"index": 635, "question": "Polygon $A_{1} A_{2} \\ldots A_{n}$ is a regular $n$-gon. For some integer $k<n$, quadrilateral $A_{1} A_{2} A_{k} A_{k+1}$ is a rectangle of area 6 . If the area of $A_{1} A_{2} \\ldots A_{n}$ is 60 , compute $n$.", "answer": "40"}, {"index": 636, "question": "A bag contains 20 lavender marbles, 12 emerald marbles, and some number of orange marbles. If the probability of drawing an orange marble in one try is $\\frac{1}{y}$, compute the sum of all possible integer values of $y$.", "answer": "69"}, {"index": 637, "question": "Compute the number of ordered quadruples of integers $(a, b, c, d)$ satisfying the following system of equations:\n\n$$\n\\left\\{\\begin{array}{l}\na b c=12,000 \\\\\nb c d=24,000 \\\\\nc d a=36,000\n\\end{array}\\right.\n$$", "answer": "12"}, {"index": 638, "question": "Let $n$ be a positive integer such that $\\frac{3+4+\\cdots+3 n}{5+6+\\cdots+5 n}=\\frac{4}{11}$. Compute $\\frac{2+3+\\cdots+2 n}{4+5+\\cdots+4 n}$.", "answer": "\\frac{27}{106}"}, {"index": 639, "question": "The quadratic polynomial $f(x)$ has a zero at $x=2$. The polynomial $f(f(x))$ has only one real zero, at $x=5$. Compute $f(0)$.", "answer": "-\\frac{32}{9}"}, {"index": 640, "question": "The Local Area Inspirational Math Exam comprises 15 questions. All answers are integers ranging from 000 to 999, inclusive. If the 15 answers form an arithmetic progression with the largest possible difference, compute the largest possible sum of those 15 answers.", "answer": "7530"}, {"index": 641, "question": "Circle $\\omega_{1}$ has center $O$, which is on circle $\\omega_{2}$. The circles intersect at points $A$ and $C$. Point $B$ lies on $\\omega_{2}$ such that $B A=37, B O=17$, and $B C=7$. Compute the area of $\\omega_{1}$.", "answer": "548\\pi"}, {"index": 642, "question": "Compute the number of integers $n$ for which $2^{4}<8^{n}<16^{32}$.", "answer": "41"}, {"index": 643, "question": "Let $T=41$. Compute the number of positive integers $b$ such that the number $T$ has exactly two digits when written in base $b$.", "answer": "35"}, {"index": 644, "question": "Let $T=35$. Triangle $A B C$ has a right angle at $C$, and $A B=40$. If $A C-B C=T-1$, compute $[A B C]$, the area of $\\triangle A B C$.", "answer": "111"}, {"index": 645, "question": "Let $x$ be a positive real number such that $\\log _{\\sqrt{2}} x=20$. Compute $\\log _{2} \\sqrt{x}$.", "answer": "5"}, {"index": 646, "question": "Let $T=5$. <PERSON> flips two fair coins, while <PERSON> flips $T$ fair coins. Let $p$ be the probability that the number of heads showing on <PERSON>'s coins is greater than the number of heads showing on <PERSON>'s coins. If $p=q / r$, where $q$ and $r$ are relatively prime positive integers, compute $q+r$.", "answer": "17"}, {"index": 647, "question": "Let $T=17$. In ARMLovia, the unit of currency is the edwah. <PERSON>'s wallet contains bills in denominations of 20 and 80 edwahs. If the bills are worth an average of $2 T$ edwahs each, compute the smallest possible value of the bills in <PERSON>'s wallet.", "answer": "1020"}, {"index": 648, "question": "Spheres centered at points $P, Q, R$ are externally tangent to each other, and are tangent to plane $\\mathcal{M}$ at points $P^{\\prime}, Q^{\\prime}, R^{\\prime}$, respectively. All three spheres are on the same side of the plane. If $P^{\\prime} Q^{\\prime}=Q^{\\prime} R^{\\prime}=12$ and $P^{\\prime} R^{\\prime}=6$, compute the area of $\\triangle P Q R$.", "answer": "18\\sqrt{6}"}, {"index": 649, "question": "Let $f(x)=x^{1}+x^{2}+x^{4}+x^{8}+x^{16}+x^{32}+\\cdots$. Compute the coefficient of $x^{10}$ in $f(f(x))$.", "answer": "40"}, {"index": 650, "question": "Compute $\\left\\lfloor 100000(1.002)^{10}\\right\\rfloor$.", "answer": "102018"}, {"index": 651, "question": "If $1, x, y$ is a geometric sequence and $x, y, 3$ is an arithmetic sequence, compute the maximum value of $x+y$.", "answer": "\\frac{15}{4}"}, {"index": 652, "question": "Define the sequence of positive integers $\\left\\{a_{n}\\right\\}$ as follows:\n\n$$\n\\left\\{\\begin{array}{l}\na_{1}=1 \\\\\n\\text { for } n \\geq 2, a_{n} \\text { is the smallest possible positive value of } n-a_{k}^{2}, \\text { for } 1 \\leq k<n .\n\\end{array}\\right.\n$$\n\nFor example, $a_{2}=2-1^{2}=1$, and $a_{3}=3-1^{2}=2$. Compute $a_{1}+a_{2}+\\cdots+a_{50}$.", "answer": "253"}, {"index": 653, "question": "Compute the base $b$ for which $253_{b} \\cdot 341_{b}=\\underline{7} \\underline{4} \\underline{X} \\underline{Y} \\underline{Z}_{b}$, for some base- $b$ digits $X, Y, Z$.", "answer": "20"}, {"index": 654, "question": "Some portions of the line $y=4 x$ lie below the curve $y=10 \\pi \\sin ^{2} x$, and other portions lie above the curve. Compute the sum of the lengths of all the segments of the graph of $y=4 x$ that lie in the first quadrant, below the graph of $y=10 \\pi \\sin ^{2} x$.", "answer": "\\frac{5\\pi}{4}\\sqrt{17}"}, {"index": 655, "question": "In equilateral hexagon $A B C D E F, \\mathrm{~m} \\angle A=2 \\mathrm{~m} \\angle C=2 \\mathrm{~m} \\angle E=5 \\mathrm{~m} \\angle D=10 \\mathrm{~m} \\angle B=10 \\mathrm{~m} \\angle F$, and diagonal $B E=3$. Compute $[A B C D E F]$, that is, the area of $A B C D E F$.", "answer": "\\frac{9}{2}"}, {"index": 656, "question": "The taxicab distance between points $A=\\left(x_{A}, y_{A}\\right)$ and $B=\\left(x_{B}, y_{B}\\right)$ is defined as $d(A, B)=$ $\\left|x_{A}-x_{B}\\right|+\\left|y_{A}-y_{B}\\right|$. Given some $s>0$ and points $A=\\left(x_{A}, y_{A}\\right)$ and $B=\\left(x_{B}, y_{B}\\right)$, define the taxicab ellipse with foci $A=\\left(x_{A}, y_{A}\\right)$ and $B=\\left(x_{B}, y_{B}\\right)$ to be the set of points $\\{Q \\mid d(A, Q)+d(B, Q)=s\\}$. Compute the area enclosed by the taxicab ellipse with foci $(0,5)$ and $(12,0)$, passing through $(1,-1)$.", "answer": "96"}, {"index": 657, "question": "The function $f$ satisfies the relation $f(n)=f(n-1) f(n-2)$ for all integers $n$, and $f(n)>0$ for all positive integers $n$. If $f(1)=\\frac{f(2)}{512}$ and $\\frac{1}{f(1)}=2 f(2)$, compute $f(f(4))$.", "answer": "4096"}, {"index": 658, "question": "<PERSON> accidentally read a degree $n$ polynomial with integer coefficients backwards. That is, he read $a_{n} x^{n}+\\ldots+a_{1} x+a_{0}$ as $a_{0} x^{n}+\\ldots+a_{n-1} x+a_{n}$. Luckily, the reversed polynomial had the same zeros as the original polynomial. All the reversed polynomial's zeros were real, and also integers. If $1 \\leq n \\leq 7$, compute the number of such polynomials such that $\\operatorname{GCD}\\left(a_{0}, a_{1}, \\ldots, a_{n}\\right)=1$.", "answer": "70"}, {"index": 659, "question": "Given a regular 16-gon, extend three of its sides to form a triangle none of whose vertices lie on the 16-gon itself. Compute the number of noncongruent triangles that can be formed in this manner.", "answer": "11"}, {"index": 660, "question": "Two square tiles of area 9 are placed with one directly on top of the other. The top tile is then rotated about its center by an acute angle $\\theta$. If the area of the overlapping region is 8 , compute $\\sin \\theta+\\cos \\theta$.", "answer": "\\frac{5}{4}"}, {"index": 661, "question": "Suppose that neither of the three-digit numbers $M=\\underline{4} \\underline{A} \\underline{6}$ and $N=\\underline{1} \\underline{B} \\underline{7}$ is divisible by 9 , but the product $M \\cdot N$ is divisible by 9 . Compute the largest possible value of $A+B$.", "answer": "12"}, {"index": 662, "question": "Let $T=12$. Each interior angle of a regular $T$-gon has measure $d^{\\circ}$. Compute $d$.", "answer": "150"}, {"index": 663, "question": "Suppose that $r$ and $s$ are the two roots of the equation $F_{k} x^{2}+F_{k+1} x+F_{k+2}=0$, where $F_{n}$ denotes the $n^{\\text {th }}$ <PERSON> number. Compute the value of $(r+1)(s+1)$.", "answer": "2"}, {"index": 664, "question": "Let $T=2$. Compute the product of $-T-i$ and $i-T$, where $i=\\sqrt{-1}$.", "answer": "5"}, {"index": 665, "question": "Let $T=5$. Compute the number of positive divisors of the number $20^{4} \\cdot 11^{T}$ that are perfect cubes.", "answer": "12"}, {"index": 666, "question": "Let $T=72 \\sqrt{2}$, and let $K=\\left(\\frac{T}{12}\\right)^{2}$. In the sequence $0.5,1,-1.5,2,2.5,-3, \\ldots$, every third term is negative, and the absolute values of the terms form an arithmetic sequence. Compute the sum of the first $K$ terms of this sequence.", "answer": "414"}, {"index": 667, "question": "Let $A$ be the sum of the digits of the number you will receive from position 7 , and let $B$ be the sum of the digits of the number you will receive from position 9 . Let $(x, y)$ be a point randomly selected from the interior of the triangle whose consecutive vertices are $(1,1),(B, 7)$ and $(17,1)$. Compute the probability that $x>A-1$.", "answer": "\\frac{79}{128}"}, {"index": 668, "question": "Let $T=9.5$. If $\\log _{2} x^{T}-\\log _{4} x=\\log _{8} x^{k}$ is an identity for all $x>0$, compute the value of $k$.", "answer": "27"}, {"index": 669, "question": "Let $T=16$. An isosceles trapezoid has an area of $T+1$, a height of 2 , and the shorter base is 3 units shorter than the longer base. Compute the sum of the length of the shorter base and the length of one of the congruent sides.", "answer": "9.5"}, {"index": 670, "question": "Let $T=10$. <PERSON> flips a fair coin $T$ times. <PERSON> has an unfair coin such that the probability of flipping heads is $\\frac{1}{3}$. <PERSON> gets to flip his coin the least number of times so that <PERSON>'s expected number of heads will exceed <PERSON>'s expected number of heads. Compute the number of times <PERSON> gets to flip his coin.", "answer": "16"}, {"index": 671, "question": "Let $T=1$. <PERSON> and <PERSON> each take 48 minutes to mow a lawn, and <PERSON> takes 24 minutes to mow a lawn. Working together, how many lawns can <PERSON>, <PERSON>, and <PERSON> mow in $2 \\cdot T$ hours? (For the purposes of this problem, you may assume that after they complete mowing a lawn, they immediately start mowing the next lawn.)", "answer": "10"}, {"index": 672, "question": "Let T be a rational number. Compute $\\sin ^{2} \\frac{T \\pi}{2}+\\sin ^{2} \\frac{(5-T) \\pi}{2}$.", "answer": "1"}, {"index": 673, "question": "Let $T=11$. Compute the value of $x$ that satisfies $\\sqrt{20+\\sqrt{T+x}}=5$.", "answer": "14"}, {"index": 674, "question": "The sum of the interior angles of an $n$-gon equals the sum of the interior angles of a pentagon plus the sum of the interior angles of an octagon. Compute $n$.", "answer": "11"}]