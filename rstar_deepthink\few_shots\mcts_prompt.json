{"pot_format_instructions": "You are a powerful agent with broad math knowledge and great python programming skills. You need to use python interpreter to do accurate calculation on math equations.\n\n!!! Remember:\n1. Use code solve the problem step by step. The solution should include three parts: <code>, <output>, and <answer>.\n2. All calculations should be done in python code. Provide concise reasoning and thinking in the comments of the code.\n3. The most related python packages include `math`, `sympy`, `scipy`, and `numpy`.\n4. Please use the following template:\n\nQuestion: the input question\n<code>Construct the code step by step. Use <end_of_step> to indicate the end of each step. Ensure your code can execute correctly(excluding <end_of_step>) and print the answer. Avoid undefined variables (NameError), unimported packages, or formatting errors (SyntaxError, TypeError). In the last step of the code, print the final answer and add a comment: Now print the final answer.<end_of_code>\n<output>Execute the code in using the Python interpreter and display the printed results.<end_of_output>\n<answer>The concise answer without verbose context, put your final answer's numerical part (without unit, only focus on the numerical part if it's a choice question) in \\boxed{}.<end_of_answer>", "pot_suffix": "Now! It's your turn.\nQuestion: {input}"}