#!/usr/bin/env python3
"""
测试FSDP安全包装器
"""

import torch
import torch.nn as nn
from transformers import PreTrainedModel, PretrainedConfig
from fsdp_safe_wrapper import (
    create_safe_reward_model, 
    apply_fsdp_safely, 
    is_fsdp_wrapped, 
    unwrap_fsdp,
    FSDPSafeRewardModel
)

# 简化的测试配置和模型
class TestConfig(PretrainedConfig):
    def __init__(self, hidden_size=768, **kwargs):
        super().__init__(**kwargs)
        self.hidden_size = hidden_size

class TestValueHead(nn.Module):
    def __init__(self, config):
        super().__init__()
        self.dropout = nn.Dropout(0.1)
        self.summary = nn.Linear(config.hidden_size, 1)
        
    def forward(self, hidden_states):
        output = self.dropout(hidden_states)
        output = self.summary(output)
        return output

class TestBaseModel(PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.embedding = nn.Embedding(1000, config.hidden_size)
        self.layers = nn.ModuleList([
            nn.TransformerEncoderLayer(config.hidden_size, 8, batch_first=True) 
            for _ in range(2)
        ])
        self._no_split_modules = ['TransformerEncoderLayer']
        
    def forward(self, input_ids, attention_mask=None, output_hidden_states=False, **kwargs):
        x = self.embedding(input_ids)
        hidden_states = [x]
        
        for layer in self.layers:
            x = layer(x)
            hidden_states.append(x)
        
        if output_hidden_states:
            class Output:
                def __init__(self, hidden_states):
                    self.hidden_states = hidden_states
            return Output(hidden_states)
        else:
            return x

def test_safe_wrapper_creation():
    """测试安全包装器的创建"""
    print("Testing safe wrapper creation...")
    
    try:
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        
        # 使用安全包装器创建RewardModel
        reward_model = FSDPSafeRewardModel(
            pretrained_model=base_model,
            v_head=TestValueHead(config),
            config=config
        )
        
        print("✓ Safe wrapper created successfully")
        print(f"✓ Has config: {hasattr(reward_model, 'config')}")
        print(f"✓ Has _no_split_modules: {hasattr(reward_model, '_no_split_modules')}")
        print(f"✓ _no_split_modules: {reward_model._no_split_modules}")
        
        # 测试前向传播
        input_ids = torch.randint(0, 1000, (2, 10))
        attention_mask = torch.ones_like(input_ids)
        
        with torch.no_grad():
            outputs = reward_model(input_ids, attention_mask)
            
        print(f"✓ Forward pass successful, output shape: {outputs.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Safe wrapper creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fsdp_status_checking():
    """测试FSDP状态检查"""
    print("\nTesting FSDP status checking...")
    
    try:
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        
        reward_model = FSDPSafeRewardModel(
            pretrained_model=base_model,
            v_head=TestValueHead(config),
            config=config
        )
        
        # 检查初始状态
        status = reward_model.check_fsdp_status()
        print("Initial FSDP status:")
        reward_model.print_fsdp_status()
        
        # 验证没有被包装
        assert not status['pretrained_model_wrapped'], "Pretrained model should not be wrapped initially"
        assert not status['v_head_wrapped'], "V head should not be wrapped initially"
        assert not status['self_wrapped'], "Self should not be wrapped initially"
        
        print("✓ FSDP status checking works correctly")
        return True
        
    except Exception as e:
        print(f"✗ FSDP status checking failed: {e}")
        return False

def test_unwrapping_functionality():
    """测试解包装功能"""
    print("\nTesting unwrapping functionality...")
    
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        
        # 手动包装基础模型
        wrapped_base = FSDP(base_model)
        print(f"✓ Base model wrapped: {is_fsdp_wrapped(wrapped_base)}")
        
        # 创建安全包装器（应该自动解包装）
        reward_model = FSDPSafeRewardModel(
            pretrained_model=wrapped_base,
            v_head=TestValueHead(config),
            config=config
        )
        
        # 检查是否正确解包装
        status = reward_model.check_fsdp_status()
        assert not status['pretrained_model_wrapped'], "Pretrained model should be unwrapped"
        
        print("✓ Unwrapping functionality works correctly")
        return True
        
    except ImportError:
        print("⚠ FSDP not available, skipping unwrapping test")
        return True
    except Exception as e:
        print(f"✗ Unwrapping functionality failed: {e}")
        return False

def test_create_safe_reward_model():
    """测试便捷创建函数"""
    print("\nTesting create_safe_reward_model function...")
    
    try:
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        
        # 使用便捷函数创建
        reward_model = create_safe_reward_model(base_model)
        
        print("✓ create_safe_reward_model works")
        print(f"✓ Model type: {type(reward_model)}")
        
        # 测试前向传播
        input_ids = torch.randint(0, 1000, (2, 10))
        with torch.no_grad():
            outputs = reward_model(input_ids)
            
        print(f"✓ Forward pass successful, output shape: {outputs.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ create_safe_reward_model failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_double_wrapping_prevention():
    """测试防止重复包装"""
    print("\nTesting double wrapping prevention...")
    
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        
        # 创建RewardModel
        reward_model = create_safe_reward_model(base_model)
        
        # 第一次包装
        wrapped_once = apply_fsdp_safely(reward_model)
        print(f"✓ First wrapping: {is_fsdp_wrapped(wrapped_once)}")
        
        # 尝试第二次包装（应该先解包装再重新包装）
        wrapped_twice = apply_fsdp_safely(wrapped_once)
        print(f"✓ Second wrapping handled safely: {is_fsdp_wrapped(wrapped_twice)}")
        
        return True
        
    except ImportError:
        print("⚠ FSDP not available, skipping double wrapping test")
        return True
    except Exception as e:
        print(f"✗ Double wrapping prevention failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("FSDP Safe Wrapper Test")
    print("=" * 60)
    
    tests = [
        test_safe_wrapper_creation,
        test_fsdp_status_checking,
        test_unwrapping_functionality,
        test_create_safe_reward_model,
        test_double_wrapping_prevention,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("Test Results")
    print("=" * 60)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    total_passed = sum(results)
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 FSDP Safe Wrapper is working correctly!")
        print("You can now use it to avoid double wrapping issues.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return total_passed == total_tests

if __name__ == "__main__":
    main()
