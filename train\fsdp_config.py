# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

"""
FSDP配置文件，用于解决RewardModelWithValueHead的FSDP包装问题
"""

import torch
from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
from torch.distributed.fsdp.wrap import transformer_auto_wrap_policy
import functools

def get_fsdp_wrap_policy():
    """
    获取适用于RewardModelWithValueHead的FSDP包装策略
    """
    try:
        from transformers.models.qwen2.modeling_qwen2 import Qwen2DecoderLayer
        transformer_layer_cls = {Qwen2DecoderLayer}
    except ImportError:
        # 如果导入失败，尝试其他可能的层类型
        try:
            from transformers.models.llama.modeling_llama import LlamaDecoderLayer
            transformer_layer_cls = {LlamaDecoderLayer}
        except ImportError:
            # 如果都失败了，使用通用的transformer层
            from transformers.models.gpt2.modeling_gpt2 import GPT2Block
            transformer_layer_cls = {GPT2Block}
    
    auto_wrap_policy = functools.partial(
        transformer_auto_wrap_policy,
        transformer_layer_cls=transformer_layer_cls
    )
    
    return auto_wrap_policy

def custom_fsdp_wrap_policy(module, recurse, nonwrapped_numel):
    """
    自定义FSDP包装策略，专门处理RewardModelWithValueHead
    """
    # 导入需要的类
    try:
        from transformers.models.qwen2.modeling_qwen2 import Qwen2DecoderLayer
        if isinstance(module, Qwen2DecoderLayer):
            return True
    except ImportError:
        pass
    
    # 检查是否是ValueHead
    if hasattr(module, '__class__') and module.__class__.__name__ == 'ValueHead':
        return True
    
    # 检查是否是embedding层或者lm_head
    if hasattr(module, '__class__'):
        class_name = module.__class__.__name__
        if any(name in class_name.lower() for name in ['embed', 'head', 'norm']):
            # 对于较小的层，检查参数数量
            if nonwrapped_numel < 1e6:  # 小于1M参数的层不包装
                return False
            return True
    
    return False

def apply_fsdp_to_reward_model(model, device_id=None):
    """
    为RewardModelWithValueHead应用FSDP包装
    
    Args:
        model: RewardModelWithValueHead实例
        device_id: CUDA设备ID，如果为None则使用当前设备
    
    Returns:
        包装后的模型
    """
    if device_id is None:
        device_id = torch.cuda.current_device()
    
    # 获取包装策略
    auto_wrap_policy = get_fsdp_wrap_policy()
    
    # 首先包装ValueHead（如果它还没有被包装）
    if not isinstance(model.v_head, FSDP):
        model.v_head = FSDP(
            model.v_head,
            device_id=device_id,
        )
        print(f"ValueHead wrapped with FSDP on device {device_id}")
    
    # 然后包装预训练模型
    if not isinstance(model.pretrained_model, FSDP):
        model.pretrained_model = FSDP(
            model.pretrained_model,
            auto_wrap_policy=auto_wrap_policy,
            device_id=device_id,
        )
        print(f"Pretrained model wrapped with FSDP on device {device_id}")
    
    return model

def setup_fsdp_for_training(model, rank=0):
    """
    为训练设置FSDP
    
    Args:
        model: 要包装的模型
        rank: 当前进程的rank
    
    Returns:
        包装后的模型
    """
    print(f"Rank {rank}: Setting up FSDP for RewardModelWithValueHead")
    
    # 确保模型在正确的设备上
    device_id = torch.cuda.current_device()
    
    # 应用FSDP包装
    model = apply_fsdp_to_reward_model(model, device_id)
    
    print(f"Rank {rank}: FSDP setup completed")
    return model
