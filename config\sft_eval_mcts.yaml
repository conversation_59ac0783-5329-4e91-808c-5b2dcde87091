mode: "mcts"          
model_dir: "" 
few_shot_path: "./rstar_deepthink/few_shots/few_shots.json"
prompt_path: "./rstar_deepthink/few_shots/sft_prompt.json" 
swap_space: 12
temperature: 1
errors_threshold: 1   # most allowed consecutive python errors
prompt_wrap: "rstar"
result_unwrap: "rstar"
step_delim: "\n"
stop: ["<end_of_step>", "<end_of_code>", "<end_of_answer>"] # "<end_of_step>",
max_depth: 16
n_generate_sample: 32
best_of: 32
is_sampling: False
iterations: 12
max_tokens: 2048
need_value_func: True
batch_size: 8000
num_few_shot: 0
max_model_len: 4096
update_leaf_value: True
c_puct: 2