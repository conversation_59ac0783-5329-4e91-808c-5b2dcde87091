mode: "mcts"          
model_dir: ""
few_shot_path: "./rstar_deepthink/few_shots/few_shots.json"
prompt_path: "./rstar_deepthink/few_shots/mcts_prompt.json" 
swap_space: 16
temperature: 0.7
errors_threshold: 2   # most allowed consecutive python errors
prompt_wrap: "rstar"
result_unwrap: "rstar"
step_delim: "\n"
stop: ["<end_of_step>", "<end_of_code>", "<end_of_output>", "<end_of_answer>"]
max_depth: 16
n_generate_sample: 12
best_of: 12
is_sampling: True
iterations: 48
max_tokens: 2048
need_value_func: False
batch_size: 2500
num_few_shot: 2
max_model_len: 4096
c_puct: 2
terminal_sample: True
llm_gpu_memory_utilization: 0.97
tp: 8