#!/bin/bash

# FSDP训练脚本，专门处理RewardModelWithValueHead的FSDP包装问题

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export MASTER_ADDR=${MASTER_ADDR:-"localhost"}
export MASTER_PORT=${MASTER_PORT:-"29500"}

# 模型和数据路径（请根据实际情况修改）
MODEL_PATH=${1:-"Qwen/Qwen2.5-Math-7B"}
DATA_PATH=${2:-"data/train_data.json"}
OUTPUT_DIR=${3:-"./output/rm_model"}

echo "Starting FSDP training for RewardModel..."
echo "Model: $MODEL_PATH"
echo "Data: $DATA_PATH"
echo "Output: $OUTPUT_DIR"

# 使用accelerate启动，避免torch.distributed.launch的一些问题
accelerate launch \
    --num_processes=8 \
    --main_process_port=$MASTER_PORT \
    train/train_RM.py \
    --model_name_or_path="$MODEL_PATH" \
    --output_dir="$OUTPUT_DIR" \
    --pair_json_path="$DATA_PATH" \
    --per_device_train_batch_size=8 \
    --per_device_eval_batch_size=8 \
    --num_train_epochs=2 \
    --gradient_accumulation_steps=2 \
    --gradient_checkpointing=True \
    --learning_rate=7e-6 \
    --remove_unused_columns=False \
    --optim="adamw_torch" \
    --logging_steps=10 \
    --eval_strategy="steps" \
    --eval_steps=500 \
    --save_steps=500 \
    --load_best_model_at_end \
    --save_total_limit=3 \
    --max_length=2048 \
    --bf16 \
    --fsdp="full_shard auto_wrap" \
    --fsdp_transformer_layer_cls_to_wrap="Qwen2DecoderLayer" \
    --dataloader_num_workers=4 \
    --warmup_ratio=0.1 \
    --weight_decay=0.01 \
    --lr_scheduler_type="cosine"

echo "Training completed!"
