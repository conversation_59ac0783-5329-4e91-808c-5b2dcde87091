#!/usr/bin/env python3
"""
安全的FSDP包装器，防止重复包装问题
"""

import torch
import torch.nn as nn
from typing import Any, Dict, Optional

def is_fsdp_wrapped(module):
    """检查模块是否已经被FSDP包装"""
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        return isinstance(module, FSDP)
    except ImportError:
        return False

def unwrap_fsdp(module):
    """如果模块被FSDP包装，则解包装"""
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        if isinstance(module, FSDP):
            return module._fsdp_wrapped_module
        return module
    except ImportError:
        return module

def safe_fsdp_wrap(module, **fsdp_kwargs):
    """安全地应用FSDP包装，避免重复包装"""
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        
        # 如果已经被包装，先解包装
        if isinstance(module, FSDP):
            print(f"Module {type(module)} is already FSDP wrapped, unwrapping first...")
            module = module._fsdp_wrapped_module
        
        # 应用FSDP包装
        wrapped_module = FSDP(module, **fsdp_kwargs)
        print(f"Successfully wrapped {type(module)} with FSDP")
        return wrapped_module
        
    except ImportError:
        print("FSDP not available, returning original module")
        return module

class FSDPSafeRewardModel(nn.Module):
    """
    FSDP安全的RewardModel包装器
    """
    
    def __init__(self, pretrained_model, v_head, config=None):
        super().__init__()
        
        # 确保子模块没有被FSDP包装
        self.pretrained_model = unwrap_fsdp(pretrained_model)
        self.v_head = unwrap_fsdp(v_head)
        
        # 设置配置
        self.config = config or getattr(pretrained_model, 'config', None)
        
        # 复制重要属性
        self._copy_attributes_from_pretrained(pretrained_model)
        
        # FSDP相关设置
        self._setup_fsdp_attributes()
        
    def _copy_attributes_from_pretrained(self, pretrained_model):
        """从预训练模型复制重要属性"""
        # 梯度检查点方法
        if hasattr(pretrained_model, "gradient_checkpointing_disable"):
            self.gradient_checkpointing_disable = pretrained_model.gradient_checkpointing_disable
        if hasattr(pretrained_model, "gradient_checkpointing_enable"):
            self.gradient_checkpointing_enable = pretrained_model.gradient_checkpointing_enable
            
        # 其他重要属性
        for attr in ['_keys_to_ignore_on_load_missing', '_keys_to_ignore_on_load_unexpected', 
                     'base_model_prefix', 'is_parallelizable', 'supports_gradient_checkpointing']:
            if hasattr(pretrained_model, attr):
                setattr(self, attr, getattr(pretrained_model, attr))
    
    def _setup_fsdp_attributes(self):
        """设置FSDP相关属性"""
        # 获取预训练模型的_no_split_modules
        pretrained_no_split = getattr(self.pretrained_model, '_no_split_modules', [])
        
        # 设置不分割的模块
        self._no_split_modules = list(pretrained_no_split)
        
        # 添加ValueHead相关的模块
        value_head_modules = ['ValueHead', 'Linear', 'Dropout']
        for module_name in value_head_modules:
            if module_name not in self._no_split_modules:
                self._no_split_modules.append(module_name)
    
    def forward(self, input_ids=None, attention_mask=None, **kwargs):
        """前向传播"""
        # 移除可能导致问题的参数
        if "factor" in kwargs:
            kwargs.pop('factor')
            
        # 确保输入在正确的设备上
        if input_ids is not None and hasattr(input_ids, 'to'):
            device = next(self.parameters()).device
            input_ids = input_ids.to(device)
            if attention_mask is not None:
                attention_mask = attention_mask.to(device)
        
        # 调用预训练模型
        kwargs["output_hidden_states"] = True
        base_model_output = self.pretrained_model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            **kwargs,
        )
        
        # 获取最后一层隐藏状态
        last_hidden_state = base_model_output.hidden_states[-1]
        
        # 通过value head
        value = self.v_head(last_hidden_state).squeeze(-1)
        
        return value
    
    def check_fsdp_status(self):
        """检查FSDP包装状态"""
        status = {
            'pretrained_model_wrapped': is_fsdp_wrapped(self.pretrained_model),
            'v_head_wrapped': is_fsdp_wrapped(self.v_head),
            'self_wrapped': is_fsdp_wrapped(self),
        }
        return status
    
    def print_fsdp_status(self):
        """打印FSDP包装状态"""
        status = self.check_fsdp_status()
        print("FSDP Wrapping Status:")
        for component, wrapped in status.items():
            print(f"  {component}: {'✓ Wrapped' if wrapped else '✗ Not wrapped'}")

def create_safe_reward_model(pretrained_model, **value_head_kwargs):
    """
    创建FSDP安全的RewardModel
    """
    from rm import ValueHead
    
    # 确保预训练模型没有被FSDP包装
    clean_pretrained_model = unwrap_fsdp(pretrained_model)
    
    # 创建ValueHead
    config = getattr(clean_pretrained_model, 'config', None)
    v_head = ValueHead(config, **value_head_kwargs)
    
    # 创建安全的RewardModel
    reward_model = FSDPSafeRewardModel(
        pretrained_model=clean_pretrained_model,
        v_head=v_head,
        config=config
    )
    
    return reward_model

def apply_fsdp_safely(model, **fsdp_kwargs):
    """
    安全地应用FSDP到RewardModel
    """
    if hasattr(model, 'check_fsdp_status'):
        print("Checking FSDP status before wrapping:")
        model.print_fsdp_status()
    
    # 确保模型没有被包装
    if is_fsdp_wrapped(model):
        print("Model is already FSDP wrapped, unwrapping...")
        model = unwrap_fsdp(model)
    
    # 应用FSDP包装
    wrapped_model = safe_fsdp_wrap(model, **fsdp_kwargs)
    
    return wrapped_model
