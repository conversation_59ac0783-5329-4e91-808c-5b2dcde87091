#!/usr/bin/env python3
"""
测试PreTrainedModel继承修复
"""

import torch
import torch.nn as nn
from transformers import PreTrainedModel, PretrainedConfig

# 简化的测试配置
class TestConfig(PretrainedConfig):
    def __init__(self, hidden_size=768, **kwargs):
        super().__init__(**kwargs)
        self.hidden_size = hidden_size

# 简化的ValueHead用于测试
class TestValueHead(nn.Module):
    def __init__(self, config):
        super().__init__()
        self.dropout = nn.Dropout(0.1)
        self.summary = nn.Linear(config.hidden_size, 1)
        
    def forward(self, hidden_states):
        output = self.dropout(hidden_states)
        output = self.summary(output)
        return output

# 简化的基础模型
class TestBaseModel(PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.embedding = nn.Embedding(1000, config.hidden_size)
        self.layers = nn.ModuleList([
            nn.TransformerEncoderLayer(config.hidden_size, 8, batch_first=True) 
            for _ in range(2)
        ])
        self._no_split_modules = ['TransformerEncoderLayer']
        
    def forward(self, input_ids, attention_mask=None, output_hidden_states=False, **kwargs):
        x = self.embedding(input_ids)
        hidden_states = [x]
        
        for layer in self.layers:
            x = layer(x)
            hidden_states.append(x)
        
        if output_hidden_states:
            class Output:
                def __init__(self, hidden_states):
                    self.hidden_states = hidden_states
            return Output(hidden_states)
        else:
            return x

# 测试版本的RewardModelWithValueHead
class TestRewardModelWithValueHead(PreTrainedModel):
    def __init__(self, pretrained_model, **kwargs):
        # 初始化PreTrainedModel
        super().__init__(pretrained_model.config)
        
        self.pretrained_model = pretrained_model
        self.v_head = TestValueHead(self.config)
        
        # 设置FSDP相关属性
        self._no_split_modules = getattr(pretrained_model, '_no_split_modules', [])
        if 'TestValueHead' not in self._no_split_modules:
            self._no_split_modules.append('TestValueHead')
            
    def forward(self, input_ids, attention_mask=None, **kwargs):
        outputs = self.pretrained_model(input_ids, attention_mask=attention_mask, 
                                       output_hidden_states=True, **kwargs)
        hidden_states = outputs.hidden_states[-1]
        values = self.v_head(hidden_states)
        return values.squeeze(-1)

def test_pretrained_model_inheritance():
    """测试PreTrainedModel继承"""
    print("Testing PreTrainedModel inheritance...")
    
    try:
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        reward_model = TestRewardModelWithValueHead(base_model)
        
        print(f"✓ Base model is PreTrainedModel: {isinstance(base_model, PreTrainedModel)}")
        print(f"✓ Reward model is PreTrainedModel: {isinstance(reward_model, PreTrainedModel)}")
        print(f"✓ Reward model has config: {hasattr(reward_model, 'config')}")
        print(f"✓ Reward model has _no_split_modules: {hasattr(reward_model, '_no_split_modules')}")
        print(f"✓ _no_split_modules content: {reward_model._no_split_modules}")
        
        # 测试前向传播
        input_ids = torch.randint(0, 1000, (2, 10))
        attention_mask = torch.ones_like(input_ids)
        
        with torch.no_grad():
            outputs = reward_model(input_ids, attention_mask)
            
        print(f"✓ Forward pass successful, output shape: {outputs.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ PreTrainedModel inheritance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fsdp_compatibility():
    """测试FSDP兼容性"""
    print("\nTesting FSDP compatibility...")
    
    try:
        from torch.distributed.fsdp import FullyShardedDataParallel as FSDP
        print("✓ FSDP import successful")
        
        # 创建测试模型
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        reward_model = TestRewardModelWithValueHead(base_model)
        
        # 检查模型是否有FSDP需要的属性
        required_attrs = ['config', '_no_split_modules']
        for attr in required_attrs:
            if hasattr(reward_model, attr):
                print(f"✓ Has {attr}: {getattr(reward_model, attr)}")
            else:
                print(f"✗ Missing {attr}")
                return False
        
        print("✓ Model has all required FSDP attributes")
        return True
        
    except Exception as e:
        print(f"✗ FSDP compatibility test failed: {e}")
        return False

def test_model_methods():
    """测试模型方法"""
    print("\nTesting model methods...")
    
    try:
        config = TestConfig(hidden_size=768)
        base_model = TestBaseModel(config)
        reward_model = TestRewardModelWithValueHead(base_model)
        
        # 测试PreTrainedModel的方法
        methods_to_test = ['parameters', 'named_parameters', 'state_dict', 'train', 'eval']
        
        for method_name in methods_to_test:
            if hasattr(reward_model, method_name):
                method = getattr(reward_model, method_name)
                if callable(method):
                    print(f"✓ Has callable method: {method_name}")
                else:
                    print(f"✓ Has attribute: {method_name}")
            else:
                print(f"✗ Missing method: {method_name}")
                return False
        
        # 测试参数计数
        total_params = sum(p.numel() for p in reward_model.parameters())
        print(f"✓ Total parameters: {total_params:,}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model methods test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("PreTrainedModel Inheritance Fix Test")
    print("=" * 60)
    
    tests = [
        test_pretrained_model_inheritance,
        test_fsdp_compatibility,
        test_model_methods,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("Test Results")
    print("=" * 60)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    total_passed = sum(results)
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 PreTrainedModel inheritance fix is working!")
        print("Your RewardModelWithValueHead should now work with FSDP.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return total_passed == total_tests

if __name__ == "__main__":
    main()
